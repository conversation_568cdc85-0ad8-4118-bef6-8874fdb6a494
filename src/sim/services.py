import datetime
import io
import uuid
from abc import ABC, abstractmethod
from collections import Counter, defaultdict
from operator import attrgetter
from typing import Binary<PERSON>, Iterable, Iterator, Type
from uuid import UUID, uuid4

from defusedxml.lxml import fromstring
from fastapi import BackgroundTasks, UploadFile
from pandas import DataFrame
from PIL import Image, UnidentifiedImageError
from pydantic import AnyHttpUrl, EmailStr
from sqlalchemy.exc import IntegrityError
from svgutils.transform import SVGFigure

from accounts.domain.exceptions import SimAccountDataNotFound
from accounts.services import AbstractAccountService
from api.sim.schemas import UploadCustomIMSI
from app.config import logger, settings
from auth.exceptions import NotFound
from cdrdata.adapters.repository import AbstractCdrRepository
from common.constants import (
    ALLOCATED,
    IMSI_FIELD,
    MSISDN_883,
    MSISDN_FIELD,
    RANGE,
    WAREHOUSE,
)
from common.file_storage import AbstractFileStorage
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import <PERSON><PERSON><PERSON>arser
from common.searching import Searching
from common.types import ICCID, IMSI
from common.types import MSISDN as msisdn_type
from common.types import (
    ContentType,
    FormFactor,
    ImageFormat,
    Month,
    UploadFileStatus,
    form_factor_mapping,
)
from email_helper.adapters.external_api import AbstractMailService  # type: ignore
from email_helper.email_helper import build_subject_and_body  # type: ignore
from rate_plans.adapters.rate_plan_repository import AbstractRatePlanRepository
from redis.adapters.externalapi import HTTPRedisAPI
from redis.service import AbstractRedisService
from sim import exceptions
from sim.adapters.repository import AbstractSimRepository
from sim.domain import model
from sim.domain.model import (
    MSISDN,
    MarketShareData,
    MarketShareModel,
    MarketSharePeriod,
    MarketShareUsage,
    MSISDNFactor,
    NotificationStatus,
    RequestType,
    SIMCardProviderAudit,
    SIMMonthlyStatus,
    SimProfileDetails,
    SimStatus,
)
from sim.domain.ports import (
    AbstractAuditService,
    AbstractMarketShareAPI,
    AbstractSIMProvisioningAPI,
)
from sim.parser import FileNameParser, ImsiCSVParser
from streaming.streaming import AbstractKafkaAPI


class MediaService:
    def __init__(self, file_storage: AbstractFileStorage):
        self.file_storage = file_storage

    @staticmethod
    def _parse_svg_image(content: bytes, width: int, height: int) -> bytes:
        fig = SVGFigure()
        fig.root = fromstring(content)
        fig.set_size([str(width), str(height)])
        return fig.to_str()

    @staticmethod
    def _parse_raster_image(content: bytes, width: int, height: int) -> bytes:
        try:
            image = Image.open(io.BytesIO(content))
        except UnidentifiedImageError:
            raise exceptions.MediaError("Image format not supported.")

        if image.height > height or image.width > width:
            image.thumbnail((width, height), Image.Resampling.LANCZOS)

        image_buf = io.BytesIO()
        image.save(image_buf, format=image.format)
        return image_buf.getvalue()

    def upload_image(
        self,
        file: UploadFile,
        folder_path: str,
        max_width: int = 100,
        max_height: int = 100,
    ) -> str:
        content_type = ContentType.parse(file.content_type)
        if content_type.tail not in ImageFormat.__dict__.values():
            raise exceptions.MediaError(
                f"Bad content type: '{file.content_type}', expected "
                f"'image/png or image/svg'."
            )
        parser = (
            self._parse_svg_image
            if content_type.mime_type == "image/svg+xml"
            else self._parse_raster_image
        )
        file_obj = parser(file.file.read(), max_width, max_height)
        image_key = f"{folder_path}/{uuid.uuid4().hex}{content_type.extension}"
        self.file_storage.upload_file(
            filename=image_key,
            file_obj=file_obj,
            content_type=content_type.mime_type,
        )
        return image_key

    def get_file_url(self, file_key: str) -> AnyHttpUrl:
        url = self.file_storage.generate_file_url(file_key)
        return AnyHttpUrl(url, scheme="https")

    def check_if_file_exits(self, file_key: str) -> None:
        if not self.file_storage.file_exists(file_key):
            raise exceptions.MediaError(f"File not found: {file_key}.")


class FakeMediaService:
    def __init__(self):
        self.files: dict[str, dict[str, bytes]] = {}

    @staticmethod
    def _parse_svg_image(content: bytes, width: int, height: int) -> bytes:
        fig = SVGFigure()
        fig.root = fromstring(content)
        fig.set_size([str(width), str(height)])
        return fig.to_str()

    @staticmethod
    def _parse_raster_image(content: bytes, width: int, height: int) -> bytes:
        try:
            image = Image.open(io.BytesIO(content))
        except UnidentifiedImageError:
            raise exceptions.MediaError("Image format not supported.")

        if image.height > height or image.width > width:
            image.thumbnail((width, height), Image.Resampling.LANCZOS)

        image_buf = io.BytesIO()
        image.save(image_buf, format=image.format)
        return image_buf.getvalue()

    def upload_image(
        self,
        file: UploadFile,
        folder_path: str,
        max_width: int = 100,
        max_height: int = 100,
    ) -> str:
        content_type = ContentType.parse(file.content_type)
        if content_type.tail not in ImageFormat.__dict__.values():
            raise exceptions.MediaError(
                f"Bad content type: '{file.content_type}', expected "
                f"'image/png or image/svg'."
            )
        parser = (
            self._parse_svg_image
            if content_type.mime_type == "image/svg+xml"
            else self._parse_raster_image
        )
        file_obj = parser(file.file.read(), max_width, max_height)
        image_key = f"{folder_path}/{uuid.uuid4().hex}{content_type.extension}"

        self.files[image_key] = {
            "content": file_obj,
            "content_type": content_type.mime_type.encode(),
        }

        return image_key

    def get_file_url(self, file_key: str) -> AnyHttpUrl:
        return AnyHttpUrl(f"https://fake-storage/{file_key}", scheme="https")

    def check_if_file_exits(self, file_key: str) -> None:
        if file_key not in self.files:
            raise exceptions.MediaError(f"File not found: {file_key}.")


class AbstractSimService(ABC):
    @abstractmethod
    def create_empty_range(
        self, title: str, form_factor: FormFactor, created_by: str
    ) -> model.Range:
        ...

    @abstractmethod
    def create_range(
        self,
        title: str,
        form_factor: FormFactor,
        client_ip: str,
        sim_cards: list[model.SIMCard],
        trace_id: UUID,
        source_endpoint: str,
        account_service: AbstractAccountService,
        streaming_service: AbstractKafkaAPI,
        account_id: int | None = None,
        created_by: str | None = None,
    ) -> model.Range:
        ...

    @abstractmethod
    def remove_range(self, range_id: int) -> None:
        ...

    @abstractmethod
    def get_ranges(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.Range], int]:
        ...

    @abstractmethod
    def get_allocations(
        self, pagination: Pagination | None = None
    ) -> tuple[list[model.AllocationSummary], int]:
        ...

    @abstractmethod
    def add_allocation(self, allocation: model.Allocation) -> model.Allocation:
        ...

    @abstractmethod
    def remove_allocation(self, allocation_id: int) -> None:
        ...

    @abstractmethod
    def remove_allocations_by_range_id(self, range_id: int) -> None:
        ...

    @abstractmethod
    def get_sim_remains(self) -> Iterable[tuple[str, FormFactor, int]]:
        ...

    @abstractmethod
    def get_sim_cards(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SIMCard]:
        ...

    @abstractmethod
    def get_sim_cards_export(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
    ) -> Iterator[model.SIMCard]:
        ...

    @abstractmethod
    def get_sim_count(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
    ) -> int:
        ...

    @abstractmethod
    def sim_status(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMStatusResponse:
        ...

    @abstractmethod
    def activate_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMActivateResponse:
        ...

    @abstractmethod
    def suspend_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMDeactivatedResponse:
        ...

    @abstractmethod
    def audit_logs(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SIMCardAuditLogs], int]:
        ...

    @abstractmethod
    def get_connection_summary(self, imsi: IMSI) -> model.ConnectionSummary:
        ...

    @abstractmethod
    def get_sim_usage(
        self,
        account_id: int,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.SimUsage], int, dict]:
        ...

    @abstractmethod
    def cards_active_statistic(
        self,
        account_id: int,
        month: Month,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.ActiveSimMonthlyStatistic], int]:
        ...

    @abstractmethod
    def get_sim_usage_export(
        self,
        account_id: int,
    ) -> Iterator[model.SimUsage]:
        ...

    @abstractmethod
    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimCDRHistory], int]:
        ...

    @abstractmethod
    def connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimCDRHistory]:
        pass

    @abstractmethod
    def get_month_usage(
        self,
        account_id: int | None = None,
    ) -> model.MonthUsage:
        ...

    @abstractmethod
    def sim_status_details(
        self,
        sim_status: SimStatus,
        pagination: Pagination | None = None,
        account_id: int | None = None,
    ) -> tuple[Iterator[model.SimStatusDetails], int]:
        ...

    @abstractmethod
    def update_sim_card_by_imsi(
        self,
        imsi: IMSI,
        client_ip: str,
        created_by: str | None = None,
    ) -> bool:
        ...

    @abstractmethod
    def bulk_background_process(
        self,
        imsi: list[IMSI],
        created_by: str,
        sim_status: model.SimStatus,
        client_ip: str | None = None,
    ) -> None:
        ...

    @abstractmethod
    def copy_monthly_statistics(self) -> int:
        ...

    @abstractmethod
    def get_imsis(
        self,
        iccid: list[ICCID],
    ) -> Iterator[model.IMSIDetails]:
        ...

    @abstractmethod
    def get_market_share_by_account(
        self, market_share_data: MarketShareData
    ) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def get_market_share(self, period: MarketSharePeriod) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimVoiceCDRHistory], int]:
        ...

    @abstractmethod
    def voice_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimVoiceCDRHistory]:
        pass

    @abstractmethod
    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimSMSCDRHistory], int]:
        ...

    @abstractmethod
    def sms_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimSMSCDRHistory]:
        pass

    @abstractmethod
    def get_market_share_imsi(
        self, period: MarketSharePeriod, imsi: IMSI
    ) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def custom_imsi_allocation(
        self,
        title: str,
        rate_plan_id: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        file_name: str,
        file: BinaryIO,
        client_ip: str,
        allocation_result: model.AllocationResult,
        streaming_service: AbstractKafkaAPI,
        trace_id: UUID,
        source_endpoint: str,
        account_service: AbstractAccountService,
        redis_service: AbstractRedisService,
        valid_imsi: list[IMSI],
        account_id: int,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
        created_by: str | None = None,
    ) -> model.AllocationResult:
        ...

    @abstractmethod
    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> bool:
        ...

    @abstractmethod
    def re_allocation_validation(
        self,
        imsi_list,
        account_id: int,
        rate_plan_id: int,
    ) -> tuple[model.ReAllocationResult, list[int]]:
        ...

    @abstractmethod
    def rate_plan_change_sim_validation(
        self,
        imsi,
        account_id: int,
        rate_plan_id: int,
    ) -> model.RatePlanChangeSimLimitResult:
        ...

    @abstractmethod
    def re_allocation(
        self,
        account_id: int,
        rate_plan_id: int,
        imsi_list: list[IMSI],
        client_ip: str,
        created_by: str,
        same_account_imsi_list: list[IMSI] | None = None,
    ) -> bool:
        ...

    @abstractmethod
    def update_msisdn(self, excel_data):
        ...

    @abstractmethod
    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: msisdn_type,
        sim_profile: model.SimProfile,
        streaming_service: AbstractKafkaAPI,
        background_tasks: BackgroundTasks,
        client_ip: str,
        created_by: str | None = None,
    ) -> model.UpdateSimCardDetailsResult:
        ...

    @abstractmethod
    def get_msisdn_factor(self, msisdn_factor: model.MSISDNFactor) -> str:
        ...

    @abstractmethod
    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[list[model.MsisdnDetails], int]:
        ...

    @abstractmethod
    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        ...

    @abstractmethod
    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:
        ...

    @abstractmethod
    def upload_msisdn(
        self,
        msisdn_list: list[MSISDN],
        invalid_format: list[str],
        duplicate_msisdn: list[MSISDN],
        client_ip: str,
        trace_id: UUID,
        source_endpoint: str,
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        uploaded_by: str | None = None,
        account_id: int | None = None,
    ) -> model.MsisdnResult:
        ...

    @abstractmethod
    def add_upload_file_status(
        self,
        trace_id: UUID,
        field: str,
        source_endpoint: str,
        client_ip: str,
        account_service: AbstractAccountService,
        account_id: int | None = None,
        account_name: str | None = None,
        success_count: int | None = None,
        failure_count: int | None = None,
        uploaded_by: str | None = None,
        message: str | None = None,
        error_results: list[dict] | None = None,
    ) -> bool:
        ...

    @abstractmethod
    def bulk_update_sim_card_details(
        self,
        total_records: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        invalid_records: list[dict],
        duplicate_imsi: list[IMSI],
        duplicate_msisdn: list[MSISDN],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[MSISDN],
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        client_ip: str,
        trace_id: UUID,
        source_endpoint: str,
        valid_data,
        uploaded_by: str | None = None,
        account_id: int | None = None,
    ) -> model.BulkSimCardUpdateResult:
        ...

    @abstractmethod
    def update_sim_card_details(
        self,
        sim_profile: model.SimProfile,
        total_records: int,
        client_ip: str,
        msisdn_factor: model.MSISDNFactor,
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        trace_id: UUID,
        source_endpoint: str,
        uploaded_by: str | None = None,
        account_id: int | None = None,
        imsi_list: list[IMSI] = [],
        msisdn_list: list[msisdn_type] = [],
        duplicate_imsi: list[IMSI] = [],
        duplicate_msisdn: list[MSISDN] = [],
        valid_data: list[dict] = [],
        invalid_data: list[dict] = [],
    ):
        ...

    @abstractmethod
    def validate_common_request(self, msisdn_list: list[msisdn_type]) -> bool:
        ...

    @abstractmethod
    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        ...

    @abstractmethod
    def imsis_to_delete(self, imsi_list: list[IMSI]) -> model.IMSIDeleteResponse:
        ...

    @abstractmethod
    def flush_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMFlushResponse:
        ...

    @abstractmethod
    def pod_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMPODResponse:
        ...

    @abstractmethod
    def sms_sim(
        self,
        imsi: IMSI,
        messasge: str,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMSendSMSResponse:
        ...

    @abstractmethod
    def apn_sim(
        self,
        imsi: IMSI,
        apn_id: str,
        created_by: str,
        sim_apn_action: model.SIMAPNAction,
        client_ip: str | None = None,
    ) -> model.SIMAPNResponse:
        ...

    @abstractmethod
    def get_latest_data_session(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
    ) -> model.DataSession:
        ...

    @abstractmethod
    def get_location(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
        page_number: int = 1,
        page_size: int = 100,
    ) -> model.LocationResponse:
        ...

    @abstractmethod
    def get_cell_location(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
    ) -> model.CellLocation:
        ...

    @abstractmethod
    def _validate_imsi_range(self, sim_cards: list[model.SIMCard]) -> bool:
        ...

    @abstractmethod
    def _validate_imsi_allocation(
        self,
        form_data: UploadCustomIMSI,
        account_id: int,
        rate_plan_id: int,
        file_name: str,
        file: BinaryIO,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
    ) -> tuple[model.AllocationResult, list[IMSI]]:
        ...

    @abstractmethod
    def worldov_token(self) -> model.TokenResponse:
        ...

    @abstractmethod
    def worldov_token_refresh(
        self, refresh_token: model.WorldOVTokenRefresh
    ) -> model.TokenResponse:
        ...

    @abstractmethod
    def fetch_mcc_mnc(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
        client_ip: str | None = None,
    ) -> model.SIMFetchMNCMCC:
        ...

    @abstractmethod
    def get_sim_card_by_iccid(
        self,
        iccid: ICCID,
    ) -> model.IMSIDetails:
        ...


class SimService(AbstractSimService):
    def __init__(
        self,
        sim_repository: AbstractSimRepository,
        rate_plan_repository: AbstractRatePlanRepository,
        provisioning: AbstractSIMProvisioningAPI,
        cdr_repository: AbstractCdrRepository,
        market_share_api: AbstractMarketShareAPI,
        audit_service: AbstractAuditService,
        media_service: MediaService,
        redis_api: HTTPRedisAPI,
        mail_service: AbstractMailService,
    ):
        self.sim_repository = sim_repository
        self.rate_plan_repository = rate_plan_repository
        self.provisioning = provisioning
        self.cdr_repository = cdr_repository
        self.market_share_api = market_share_api
        self.audit_service = audit_service
        self.media = media_service
        self.redis_api = redis_api
        self.mail_service = mail_service

    def _check_sim_limit(
        self, sim_count: int, rate_plan_sim_count: int, sim_limit: int | None = None
    ):

        if sim_limit and (sim_count > sim_limit):
            allowed_sim_to_allocate = sim_limit - rate_plan_sim_count
            raise exceptions.SimLimitCountError(
                f"Unable to assign SIMs. There are currently {rate_plan_sim_count}"
                " SIMs assigned to this rate plan."
                f" {allowed_sim_to_allocate} SIMs can be added"
            )

    def create_empty_range(
        self, title: str, form_factor: FormFactor, created_by: str
    ) -> model.Range:
        range_ = model.Range(
            title=title,
            form_factor=form_factor,
            created_by=created_by,
        )
        self.sim_repository.add_range(range_)
        return range_

    def remove_range(self, range_id: int) -> None:
        range_ = self.sim_repository.get_range_by_id(range_id)
        if range_ is None:
            raise exceptions.RangeDoesNotExist(range_id)
        self.sim_repository.remove_range(range_)

    def _create_msisdn_pool(
        self, msisdns: list[MSISDN], uploaded_by: str | None = None
    ) -> list[model.MsisdnPool]:
        """
        MSISDN Factor will be set to NATIONAL default
        NATIONAL if msisdn starts with 447 or any other number
        INTERNATIONAL if msisdn starts with 883
        """
        return [
            model.MsisdnPool(
                msisdn=msisdn,
                sim_profile=model.SimProfile.DATA_ONLY,
                msisdn_factor=MSISDNFactor.INTERNATIONAL
                if msisdn.startswith("883")
                else MSISDNFactor.NATIONAL,
                uploaded_by=uploaded_by,
                created_at=datetime.datetime.today(),
            )
            for msisdn in msisdns
        ]

    def _create_sim_eid(self, sim_cards: list[model.SIMCard]) -> list[model.SIMEID]:
        return [
            model.SIMEID(eid=sim_card.sim_eid, sim_card_id=sim_card.id)
            for sim_card in sim_cards
        ]

    @staticmethod
    def _generate_and_push_sim_activity_audit(
        pool_msisdn: list[model.MsisdnPool],
        sim_cards: list[model.SIMCard],
        client_ip: str,
        streaming_service: AbstractKafkaAPI,
        created_by: str | None = None,
    ) -> bool:
        logger.info("Background task started")
        audit_details_pool = []
        audit_detail_sim = []
        for data, sim in zip(pool_msisdn, sim_cards):
            # MSISDN Pool Audit (3 entries per data)
            audit_details_pool.extend(
                [
                    model.MsisdnPoolAudit(
                        uuid=uuid4(),
                        msisdn=data.msisdn,
                        request_type=RANGE,
                        prior_value=WAREHOUSE,
                        new_value=data.msisdn,
                        field=MSISDN_FIELD,
                        action="Uploaded",
                        client_ip=client_ip,
                        created_by=created_by,
                    ),
                    model.MsisdnPoolAudit(
                        uuid=uuid4(),
                        msisdn=data.msisdn,
                        request_type=RANGE,
                        prior_value=WAREHOUSE,
                        new_value=model.SimProfile.DATA_ONLY.name,
                        field="SIM Profile",
                        action="Uploaded",
                        client_ip=client_ip,
                        created_by=created_by,
                    ),
                    model.MsisdnPoolAudit(
                        uuid=uuid4(),
                        msisdn=data.msisdn,
                        request_type=RANGE,
                        prior_value=WAREHOUSE,
                        new_value=data.msisdn_factor,
                        field="MSISDN Type",
                        action="Uploaded",
                        client_ip=client_ip,
                        created_by=created_by,
                    ),
                ]
            )
            # SIM Card Audit (1 entry per sim)
            audit_detail_sim.append(
                model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=sim.imsi,
                    iccid=ICCID(str(sim.iccid)),
                    msisdn=sim.msisdn,
                    request_type=RANGE,
                    prior_value=WAREHOUSE,
                    new_value=sim.imsi,
                    field=IMSI_FIELD,
                    action="Uploaded",
                    client_ip=client_ip,
                    created_by=created_by,
                )
            )
        streaming_service.submit_to_kafka(
            key=RANGE,
            topic=settings.UPLOAD_MSISDN_TOPIC,
            audit_details=audit_details_pool,
        )
        streaming_service.submit_to_kafka(
            key=RANGE,
            topic=settings.SIM_ACTIVITY_TOPIC,
            audit_details=audit_detail_sim,
        )
        logger.info("Background task completed")
        return True

    def create_range(
        self,
        title: str,
        form_factor: FormFactor,
        client_ip: str,
        sim_cards: list[model.SIMCard],
        trace_id: UUID,
        source_endpoint: str,
        account_service: AbstractAccountService,
        streaming_service: AbstractKafkaAPI,
        account_id: int | None = None,
        created_by: str | None = None,
    ) -> model.Range:
        """Create a new range with pre-validated SIM cards."""
        unique_msisdns = list({sim_card.msisdn for sim_card in sim_cards})
        imsi_first, imsi_last = sim_cards[0].imsi, sim_cards[-1].imsi
        range_ = model.Range(
            title=title,
            form_factor=form_factor,
            created_by=created_by,
            imsi_first=imsi_first,
            imsi_last=imsi_last,
        )
        range_.sim_cards = sim_cards

        self.sim_repository.flush_range(range_)

        pool_msisdn = self._create_msisdn_pool(
            msisdns=unique_msisdns, uploaded_by=created_by
        )
        sim_eid = None
        if form_factor == FormFactor.eSIM_MFF2_eUICC:
            sim_eid = self._create_sim_eid(sim_cards)

        self.sim_repository.add_range(range_, pool_msisdn, sim_eid)

        self.add_upload_file_status(
            trace_id=trace_id,
            uploaded_by=created_by,
            client_ip=client_ip,
            source_endpoint=source_endpoint,
            field=IMSI_FIELD,
            success_count=len(sim_cards),
            failure_count=None,
            error_results=None,
            account_service=account_service,
            account_id=account_id,
        )

        self._generate_and_push_sim_activity_audit(
            pool_msisdn=pool_msisdn,
            sim_cards=sim_cards,
            client_ip=client_ip,
            created_by=created_by,
            streaming_service=streaming_service,
        )

        return range_

    def _validate_imsi_range(self, sim_cards: list[model.SIMCard]) -> bool:
        imsi_first, imsi_last = sim_cards[0].imsi, sim_cards[-1].imsi
        if self.sim_repository.check_imsi_range(imsi_first, imsi_last):
            logger.error(
                f"SIM cards already exist for range from {imsi_first} to {imsi_last}."
            )
            raise exceptions.SimCardImsiAlreadyExist(
                f"SIM cards already exist for range from {imsi_first} to {imsi_last}"
            )

        # Check for duplicate MSISDNs
        unique_msisdns = list({sim_card.msisdn for sim_card in sim_cards})
        existing_in_pool = set(
            self.sim_repository.get_msisdn_in_pool_msisdn_table(
                msisdn_list=unique_msisdns
            )
        )
        existing_in_simcard = set(
            self.sim_repository.get_msisdn_in_sim_card_table(msisdn_list=unique_msisdns)
        )
        already_existing_msisdns = existing_in_pool | existing_in_simcard
        if already_existing_msisdns:
            logger.error(
                "Msisdn already exist error:- "
                f"existing in Msisdn pool: {existing_in_pool} "
                f"existing in Sim Card: {existing_in_simcard}"
            )
            raise exceptions.MSISDNExitError("MSISDN(s) already exist in the system.")

        # Check for duplicate EIDs
        unique_eids = list({sim_card.sim_eid for sim_card in sim_cards})
        existing_in_sim_eid = set(
            self.sim_repository.get_eid_in_sim_eid_table(eid_list=unique_eids)
        )
        if existing_in_sim_eid:
            logger.error(f"EID already exist in sim eid:{existing_in_sim_eid}")
            raise exceptions.EIDExitError("EID(s) already exist in the system.")
        return True

    def get_ranges(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.Range], int]:
        range_count = self.sim_repository.get_range_list_count(searching=searching)
        records = self.sim_repository.get_range_list(
            searching=searching, pagination=pagination, ordering=ordering
        )
        return records, range_count

    def _get_logo_url(self, logo_key: str | None) -> AnyHttpUrl | None:
        try:
            if logo_key is None:
                return logo_key
            return self.media.get_file_url(logo_key)
        except Exception as e:
            logger.error(str(e), exc_info=True)
            return None

    def get_allocations(
        self, pagination: Pagination | None = None
    ) -> tuple[list[model.AllocationSummary], int]:
        response, total_count = self.sim_repository.add_allocations_details(
            pagination=pagination
        )
        if not response:
            raise exceptions.NotFound("No data found")
        for allocation in response:
            allocation.logo_url = self._get_logo_url(allocation.logo_key)
        return response, total_count

    def add_allocation(self, allocation: model.Allocation) -> model.Allocation:
        if allocation.rate_plan_id is None:
            raise AssertionError("The allocation must have an assigned rate_plan")

        rate_plan = self.rate_plan_repository.get(allocation.rate_plan_id)

        if not rate_plan:
            raise exceptions.RatePlanNotFound(
                f"Rate Plan with id:{allocation.rate_plan_id} does not exist"
            )

        if rate_plan.account_id != allocation.account_id:
            raise exceptions.RatePlanAccountMappingError(
                "Incorrect account for the specified Rate Plan"
            )
        range_ = self.sim_repository.get_range_by_id(allocation.range_id)

        if range_ is None:
            raise exceptions.RangeDoesNotExist(allocation.range_id)

        allocation = range_.allocate(allocation)
        self.sim_repository.update_range(range_)
        self.sim_repository.update_sim_cards_with_allocation(allocation)
        return allocation

    def remove_allocations_by_range_id(self, range_id: int) -> None:
        range_ = self.sim_repository.get_range_by_id(range_id)
        if range_ is None:
            raise exceptions.RangeDoesNotExist(range_id)
        return self.sim_repository.remove_allocations_in_range(range_)

    def remove_allocation(self, allocation_id: int) -> None:
        allocation = self.sim_repository.get_allocation_by_id(allocation_id)
        if allocation is None:
            raise exceptions.AllocationDoesNotExist(allocation_id)
        last_allocation = self.sim_repository.get_last_allocation_in_range(
            allocation.range_id
        )
        if last_allocation is None:
            raise exceptions.AllocationDoesNotExist(allocation_id)
        return self.sim_repository.remove_allocation(allocation)

    def get_sim_remains(self) -> Iterable[tuple[str, FormFactor, int]]:
        for form_factor, remaining in self.sim_repository.get_sim_remains():
            yield "NR", form_factor, remaining

    def get_sim_cards(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SIMCard]:
        yield from self.sim_repository.get_sim_cards(
            account_id=account_id,
            rate_plan_ids=rate_plan_ids,
            pagination=pagination,
            ordering=ordering,
            imsi_list=imsi_list,
            creation_month=creation_month,
            searching=searching,
        )

    def get_sim_cards_export(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
    ) -> Iterator[model.SIMCard]:
        yield from self.sim_repository.get_sim_cards(
            account_id=account_id,
            rate_plan_ids=rate_plan_ids,
            imsi_list=imsi_list,
            creation_month=creation_month,
        )

    def get_sim_count(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
        deactivated_only: bool = False,
        pending_only: bool = False,
        ready_activation_only: bool = False,
        sim_status: SimStatus | None = None,
    ) -> int:
        return self.sim_repository.get_sim_count(
            account_id=account_id,
            rate_plan_ids=rate_plan_ids,
            creation_month=creation_month,
            imsi_list=imsi_list,
            searching=searching,
            active_only=active_only,
            deactivated_only=deactivated_only,
            pending_only=pending_only,
            ready_activation_only=ready_activation_only,
            sim_status=sim_status,
        )

    def get_sim_card(self, imsi: IMSI):
        """Get SIM card object of single sim"""
        sim_cards = self.sim_repository.get_sim_cards(imsi_list=[imsi])
        try:
            sim_card = next(sim_cards)
            if sim_card.allocation_id is None:
                logger.error(f"IMSI allocation not found:- {imsi}")
                raise exceptions.AllocationError("IMSI allocation not found.")
            if sim_card.sim_profile is None:
                logger.error(f"Sim-MSISDN sim_profile not found:- {imsi}")
                raise exceptions.SimError("Sim profile not found.")
        except StopIteration:
            logger.error(f"Requested IMSI not found:- {imsi}")
            raise exceptions.SimCardsNotFound("Requested IMSI not found.")
        return sim_card

    def sim_status(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMStatusResponse:
        """Function for get SIM status"""
        sim_card = self.get_sim_card(IMSI(imsi))
        iccid = sim_card.iccid
        msisdn = sim_card.msisdn
        pip_response = self.provisioning.sim_status(IMSI(imsi), MSISDN(msisdn))
        if (SimStatus(sim_card.sim_status) != pip_response.sim_status) and (
            sim_card.sim_status != SimStatus.PENDING
        ):
            if (SimStatus(sim_card.sim_status) != SimStatus.READY_FOR_ACTIVATION) or (
                SimStatus(sim_card.sim_status) == SimStatus.READY_FOR_ACTIVATION
                and pip_response.sim_status != SimStatus.DEACTIVATED
            ):

                audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(imsi),
                    iccid=ICCID(iccid),
                    msisdn=MSISDN(msisdn),
                    request_type="Get status",
                    prior_value=SimStatus(sim_card.sim_status),
                    new_value=SimStatus(pip_response.sim_status),
                    field="Status",
                    action="Updated",
                    client_ip=client_ip,
                    created_by=created_by,
                    audit_date=datetime.datetime.today(),
                    message="System",
                    status=pip_response.sim_status,
                    work_id=imsi,
                    prior_status=sim_card.sim_status,
                )
                result = self.audit_service.add_sim_audit_api([audit_detail])

                sim_provider_log = SIMCardProviderAudit(
                    sim_activity_log_uuid=audit_detail.uuid,
                    activity_id=str(result["id"][0]),
                    audit_date=datetime.datetime.today(),
                    message="System",
                    status=pip_response.sim_status,
                    work_id=imsi,
                    prior_status=sim_card.sim_status,
                )

                self.sim_repository.update_sim_card(
                    imsi, SimStatus(pip_response.sim_status)
                )
                sim_monthly_status = self._get_sim_monthly_status(
                    IMSI(imsi), sim_provider_log
                )
                if SimStatus(sim_card.sim_status) == SimStatus.READY_FOR_ACTIVATION:
                    sim_monthly_status.is_first_activation = False
                self._add_sim_monthly_status(
                    sim_monthly_status,
                    SimStatus(pip_response.sim_status),
                )
        return pip_response

    def activate_sim_using_provisioning(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        prior_status: SimStatus,
        valid_sim_profile: str,
        client_ip: str | None = None,
    ):
        """Activate the SIM using the provisioning object and log the result"""
        ppl_provision = self.provisioning.activate_sim(
            imsi, MSISDN(msisdn), SimStatus(prior_status), valid_sim_profile, client_ip
        )
        if ppl_provision.status == "Invalid":
            raise exceptions.SimActivationError("SIM already activated.")
        sim_status = SimStatus.PENDING
        self.sim_repository.update_sim_card(IMSI(imsi), SimStatus(sim_status))
        return ppl_provision

    def create_sim_response(
        self, audit_uuid: str, ppl_provision: model.SIMActivateResponse
    ) -> model.SIMActivateResponse:
        """Create SIM activation response object using the provisioning result"""
        sim_response = model.SIMActivateResponse(
            uuid=audit_uuid,
            message=ppl_provision.message,
            status=ppl_provision.status,
        )
        return sim_response

    def _validate_sim_profile_code(self, sim_profile: model.SimProfile) -> str:
        valid_sim_profile = SimProfileDetails.get(sim_profile.name)
        if not valid_sim_profile:
            logger.error(
                f"Requested Sim Profile:- {sim_profile} ,"
                "Invalid Sim Profile requested"
            )
            raise exceptions.SimAccountProfileError("Invalid Sim Profile requested")
        return valid_sim_profile

    def activate_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMActivateResponse:
        """Function for activating SIM"""
        sim_card = self.get_sim_card(IMSI(imsi))
        iccid = sim_card.iccid
        msisdn = sim_card.msisdn
        prior_status = sim_card.sim_status
        sim_profile = sim_card.sim_profile
        logger.debug(f"Sim Profile:- {sim_profile}")

        valid_sim_profile = self._validate_sim_profile_code(sim_profile=sim_profile)

        audit_detail = model.SIMCardAudit(
            uuid=uuid4(),
            imsi=IMSI(imsi),
            iccid=ICCID(iccid),
            msisdn=MSISDN(msisdn),
            request_type=model.RequestType.PROVIDE.name,
            prior_value=SimStatus(sim_card.sim_status),
            new_value=SimStatus.ACTIVE,
            field="Status",
            action="Updated",
            client_ip=client_ip,
            created_by=created_by,
        )
        result = self.audit_service.add_sim_audit_api([audit_detail])

        ppl_provision = self.activate_sim_using_provisioning(
            imsi, MSISDN(msisdn), prior_status, valid_sim_profile, client_ip
        )

        audit_provider_detail = model.SIMCardProviderAudit(
            sim_activity_log_uuid=audit_detail.uuid,
            activity_id=str(result["id"][0]),
            audit_date=ppl_provision.audit_date,
            message=ppl_provision.message,
            status=ppl_provision.status,
            work_id=ppl_provision.work_id,
            prior_status=ppl_provision.prior_status,
        )
        self.audit_service.add_sim_provider_audit_api(audit_provider_detail)

        sim_response = self.create_sim_response(result["id"][0], ppl_provision)
        return sim_response

    def deactivate_sim_using_suspend(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        prior_status: SimStatus,
        client_ip: str | None = None,
    ):
        """Deactivate the SIM using the suspend object and log the result"""
        ppl_suspend = self.provisioning.suspend_sim(
            MSISDN(msisdn), SimStatus(prior_status), client_ip
        )
        if ppl_suspend.status == "Error":
            raise exceptions.SimDeActivationError("SIM already deactivated.")
        sim_status = SimStatus.PENDING
        self.sim_repository.update_sim_card(IMSI(imsi), SimStatus(sim_status))
        return ppl_suspend

    def create_sim_response_suspend(
        self, audit_uuid: str, ppl_suspend: model.SIMDeactivatedResponse
    ) -> model.SIMDeactivatedResponse:
        """Create SIM deactivation response object using the suspend result"""
        sim_response = model.SIMDeactivatedResponse(
            uuid=audit_uuid,
            message=ppl_suspend.message,
            status=ppl_suspend.status,
        )
        return sim_response

    def suspend_sim(
        self, imsi: IMSI, created_by: str, client_ip: str | None = None
    ) -> model.SIMDeactivatedResponse:
        """Function for suspend SIM"""
        sim_card = self.get_sim_card(IMSI(imsi))
        iccid = sim_card.iccid
        msisdn = sim_card.msisdn
        prior_status = sim_card.sim_status

        audit_detail = model.SIMCardAudit(
            uuid=uuid4(),
            imsi=IMSI(imsi),
            iccid=ICCID(iccid),
            msisdn=MSISDN(msisdn),
            request_type=model.RequestType.CEASE.name,
            prior_value=SimStatus(sim_card.sim_status),
            new_value=SimStatus.DEACTIVATED,
            field="Status",
            action="Updated",
            client_ip=client_ip,
            created_by=created_by,
        )
        result = self.audit_service.add_sim_audit_api([audit_detail])

        ppl_suspend = self.deactivate_sim_using_suspend(
            IMSI(imsi), MSISDN(msisdn), prior_status, client_ip
        )

        audit_provider_detail = model.SIMCardProviderAudit(
            sim_activity_log_uuid=audit_detail.uuid,
            activity_id=str(result["id"][0]),
            audit_date=ppl_suspend.audit_date,
            message=ppl_suspend.message,
            status=ppl_suspend.status,
            work_id=ppl_suspend.work_id,
            prior_status=ppl_suspend.prior_status,
        )
        self.audit_service.add_sim_provider_audit_api(audit_provider_detail)

        sim_response = self.create_sim_response_suspend(result["id"][0], ppl_suspend)
        return sim_response

    def audit_logs(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SIMCardAuditLogs], int]:
        """Function for audit logs of activate and deactivate"""

        audit_logs = self.audit_service.get_sim_audit_api(
            imsi=imsi,
            month=month,
            is_client=is_client,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        if audit_logs.totalCount == 0:
            raise exceptions.NoAuditLogs(imsi, month)
        return audit_logs.results, audit_logs.totalCount

    def get_connection_summary(self, imsi: IMSI) -> model.ConnectionSummary:
        last_session = self.cdr_repository.get_cdr_last_session(imsi)
        connection_history_summary = self.sim_repository.get_connection_summary(imsi)
        if connection_history_summary.rate_plan_id is not None:
            get_rate_plan = self.rate_plan_repository.get(
                connection_history_summary.rate_plan_id
            )

            if get_rate_plan is not None:
                rate_plan_name = get_rate_plan.name
            else:
                rate_plan_name = None
        connection_history_summary.last_session = last_session
        connection_history_summary.rate_plan = rate_plan_name
        return connection_history_summary

    def _sim_usage_summary_total(
        slef, sim, sim_usage_analytics, carrier_name_list
    ) -> int:
        filtering_sim_data = filter(lambda x: x.imsi == sim.imsi, sim_usage_analytics)
        sim_usage_summary_total = sum(
            map(
                lambda sim_data: sum(
                    map(
                        lambda sim_usage_summary: (
                            sim_usage_summary.usage  # type: ignore
                        ),
                        filter(
                            lambda summary: summary.carrier  # type: ignore
                            in map(lambda x: x.carrier, carrier_name_list),
                            sim_data.usageSummary,
                        ),
                    )
                ),
                filtering_sim_data,
            )
        )
        return sim_usage_summary_total

    def _merge_sim_managment_with_usage(
        self, sim_list, sim_usage, sim_usage_analytics, carrier_name_list
    ) -> Iterator[model.SimUsage]:
        """Function for compare sim iccid and push usage"""
        usage_dict = {row.iccid: row.usage for row in sim_usage}

        for sim in sim_list:
            sim_usage_summary_total = self._sim_usage_summary_total(
                sim, sim_usage_analytics, carrier_name_list
            )
            usage = usage_dict.get(sim.iccid)
            if sim_usage_summary_total is not None:
                if usage is None:
                    usage = 0
                sim_with_usage = model.SimUsage(
                    sim_id=sim.sim_id,
                    iccid=sim.iccid,
                    msisdn=sim.msisdn,
                    imsi=sim.imsi,
                    eid=sim.eid,
                    type=sim.type,
                    allocation_reference=sim.allocation_reference,
                    allocation_date=sim.allocation_date,
                    sim_status=sim.sim_status,
                    usage=usage,
                    rate_plan=sim.rate_plan,
                    ee_usage=sim_usage_summary_total,
                    sim_profile=sim.sim_profile,
                    msisdn_factor=sim.msisdn_factor,
                )
                yield sim_with_usage
            else:
                yield sim

    def get_sim_usage(
        self,
        account_id: int,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.SimUsage], int, dict]:
        carrier_name_data = "EE"
        carrier_name = self.sim_repository.get_carrier_name(
            carrier_name=carrier_name_data
        )
        carrier_name_list = list(carrier_name)

        (
            total,
            totalActiveSims,
            totalDeactivatedSims,
            totalPendingSims,
            totalReadyActivationSims,
        ) = self.sim_repository.get_sim_usage_count(
            account_id=account_id, searching=searching
        )  # Function Updated

        if total == 0:
            raise exceptions.NoSimFound()

        sim_cards = self.get_sim_cards(account_id=account_id)
        if any(sim_card.rate_plan_id is None for sim_card in sim_cards):
            raise exceptions.RatePlanNotFound("Rate Plan not found.")

        sim_data = self.sim_repository.get_sim_usage(
            account_id, ordering=ordering, searching=searching, pagination=pagination
        )
        sim_list = list(sim_data)
        imsi = [IMSI(sim.imsi) for sim in sim_list]
        today = datetime.date.today()
        month_ = Month(year=today.year, month=today.month, day=1)
        sim_usage = self.cdr_repository.get_sim_usage_by_imsi(imsi, month_)
        sim_usage_analytics_data = model.MarketShareModel(
            from_date=None,
            to_date=None,
            imsis=imsi,
        )
        sim_usage_analytics = self.market_share_api.get_imsis_usage(
            sim_usage_analytics_data
        )
        sim_management: Iterator[model.SimUsage] = self._merge_sim_managment_with_usage(
            sim_list, sim_usage, sim_usage_analytics.summary, carrier_name_list
        )

        # calculates statistics
        imsi_list = self.sim_repository.get_sim_imsi_by_account_id(
            account_id=account_id,
            searching=searching,
        )

        total_usage = self.cdr_repository.get_sim_usage_total_by_imsi(
            imsi_list, month_  # type: ignore
        )

        sim_usage_analytics_data = model.MarketShareModel(
            from_date=None,
            to_date=None,
            imsis=imsi_list,  # type: ignore
        )
        sim_usage_analytics = self.market_share_api.get_imsis_usage(
            sim_usage_analytics_data
        )
        totalEEUsageData = 0
        for sim in sim_list:
            ee_usage = self._sim_usage_summary_total(
                sim, sim_usage_analytics.summary, carrier_name_list
            )
            totalEEUsageData = totalEEUsageData + ee_usage

        statistics_dict = {
            "totalUsage": total_usage,
            "totalEEUsageData": totalEEUsageData,
            "totalActiveSims": totalActiveSims,
            "totalReadyActivationSims": totalReadyActivationSims,
            "totalDeactivatedSims": totalDeactivatedSims,
            "totalPendingSims": totalPendingSims,
            "totalSims": total,
        }
        return sim_management, total, statistics_dict

    def get_sim_usage_export(
        self,
        account_id: int,
    ) -> Iterator[model.SimUsage]:
        carrier_name_data = "EE"
        carrier_name = self.sim_repository.get_carrier_name(
            carrier_name=carrier_name_data
        )
        carrier_name_list = list(carrier_name)

        sim_cards = self.get_sim_cards(account_id=account_id)
        if any(sim_card.rate_plan_id is None for sim_card in sim_cards):
            raise exceptions.RatePlanNotFound("Rate Plan not found.")

        sim_data = self.sim_repository.get_sim_usage(account_id)
        sim_list = list(sim_data)
        imsi = [IMSI(sim.imsi) for sim in sim_list]
        today = datetime.date.today()
        month_ = Month(year=today.year, month=today.month, day=1)
        sim_usage = self.cdr_repository.get_sim_usage_by_imsi(imsi, month_)
        sim_usage_analytics_data = model.MarketShareModel(
            from_date=None,
            to_date=None,
            imsis=imsi,
        )
        sim_usage_analytics = self.market_share_api.get_imsis_usage(
            sim_usage_analytics_data
        )
        sim_management: Iterator[model.SimUsage] = self._merge_sim_managment_with_usage(
            sim_list, sim_usage, sim_usage_analytics.summary, carrier_name_list
        )
        return sim_management

    def _merge_sim_with_usage(
        self, active_sim_list, active_sims_usage
    ) -> Iterator[model.ActiveSimMonthlyStatistic]:
        """Function for compare sim iccid and push usage"""
        usage_dict = {row.iccid: row.usage for row in active_sims_usage}
        for sim in active_sim_list:
            usage = usage_dict.get(sim.iccid)
            if usage is not None:
                sim_with_usage = model.ActiveSimMonthlyStatistic(
                    id=sim.id,
                    imsi=sim.imsi,
                    iccid=sim.iccid,
                    msisdn=sim.msisdn,
                    sim_status=sim.sim_status,
                    is_first_activation=sim.is_first_activation,
                    usage=usage,
                    rate_plan_id=sim.rate_plan_id,
                )
                yield sim_with_usage
            else:
                yield sim

    def cards_active_statistic(
        self,
        account_id: int,
        month: Month,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.ActiveSimMonthlyStatistic], int]:
        """Function for get active sim,usage, and first activation for the month"""
        active_sims = self.sim_repository.cards_active_statistic(
            account_id=account_id, month=month, pagination=pagination
        )
        active_list = list(active_sims)
        imsi = [IMSI(sim.imsi) for sim in active_list]
        active_sim_usage = self.cdr_repository.get_sim_usage_by_imsi(imsi, month)

        sim_usage: Iterator[
            model.ActiveSimMonthlyStatistic
        ] = self._merge_sim_with_usage(active_list, active_sim_usage)

        total = self.sim_repository.cards_active_statistic_count(
            account_id=account_id, month=month
        )
        if total == 0:
            raise exceptions.NoActiveSimFound(account_id, month)
        return sim_usage, total

    def connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimCDRHistory]:

        connection_history = self.cdr_repository.connection_history(
            imsi=imsi,
            month=month,
        )
        converted_history = (
            model.SimCDRHistory(**item.__dict__) for item in connection_history
        )
        return converted_history

    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimCDRHistory], int]:
        """Function for connection history"""

        response = self.check_imsi_account(account_id=account_id, imsi=[imsi])
        logger.info(f"#####account_id:-{account_id}")
        if response:
            connection_history = self.cdr_repository.connection_history(
                imsi=imsi,
                month=month,
                pagination=pagination,
                searching=searching,
            )
            converted_history = (
                model.SimCDRHistory(**item.__dict__) for item in connection_history
            )
            total = self.cdr_repository.connection_history_count(
                imsi=imsi,
                month=month,
                searching=searching,
            )

            if total == 0:
                raise exceptions.NoConnectionHistory(imsi, month)
            return converted_history, total
        else:
            raise exceptions.IMSIDoesNotExit(imsi)

    def get_month_usage(self, account_id: int | None = None) -> model.MonthUsage:
        iccid_list = [
            ICCID(sim_card.iccid)
            for sim_card in self.get_sim_cards(account_id=account_id)
        ]
        if len(iccid_list) == 0:
            raise exceptions.NoSimFound()
        total_usage = self.cdr_repository.usage_monthly_total(iccid=iccid_list) or 0
        total_sims = self.get_sim_count(account_id=account_id)
        total_active_sims = self.get_sim_count(account_id=account_id, active_only=True)
        total_deactivated_sims = self.get_sim_count(
            account_id=account_id, deactivated_only=True
        )
        total_pending_sims = self.get_sim_count(
            account_id=account_id, pending_only=True
        )
        total_ready_activation_sims = self.get_sim_count(
            account_id=account_id, ready_activation_only=True
        )
        result = model.MonthUsage(
            total_usage=total_usage,
            total_sims=total_sims,
            total_active_sims=total_active_sims,
            total_deactivated_sims=total_deactivated_sims,
            total_pending_sims=total_pending_sims,
            total_ready_activation_sims=total_ready_activation_sims,
        )
        return result

    def sim_status_details(
        self,
        sim_status: SimStatus,
        pagination: Pagination | None = None,
        account_id: int | None = None,
    ) -> tuple[Iterator[model.SimStatusDetails], int]:
        total = self.sim_repository.sim_status_details_count(
            sim_status=sim_status,
            account_id=account_id,
        )
        logger.info(
            f"/cards/{{sim_status}} total sims with status {sim_status} : {total}"
        )
        if total == 0:
            raise exceptions.NoSimCardsStatus(sim_status)
        sim_response = self.sim_repository.sim_status_details(
            sim_status=sim_status,
            pagination=pagination,
            account_id=account_id,
        )
        converted_status = (
            model.SimStatusDetails(**item.__dict__) for item in sim_response
        )
        return converted_status, total

    def _get_sim_card(self, imsi: IMSI):
        """Get SIM card object of single sim"""
        sim_cards = self.sim_repository.get_sim_cards(imsi_list=[imsi])
        try:
            sim_card = next(sim_cards)
        except StopIteration:
            raise exceptions.SimCardsNotFound("Requested reference not found.")
        return sim_card

    def _get_sim_monthly_status(
        self, reference: IMSI, notification: SIMCardProviderAudit
    ):
        sim_card = self._get_sim_card(IMSI(reference))
        year = notification.audit_date.year
        month = notification.audit_date.month
        sim_status = SimStatus(notification.status)
        sim_monthly_status = SIMMonthlyStatus(
            sim_card_id=sim_card.id,
            month=Month(year=year, month=month, day=1),
            sim_status=sim_status,
            is_first_activation=True,
        )
        return sim_monthly_status

    def _add_sim_monthly_status(
        self, sim_monthly_status: SIMMonthlyStatus, sim_status: SimStatus
    ):
        is_sim_fee_applicable = self.sim_repository.check_sim_monthly_status(
            sim_monthly_status.sim_card_id, None
        )
        if is_sim_fee_applicable:
            if not self.sim_repository.check_sim_monthly_status(
                sim_monthly_status.sim_card_id, sim_monthly_status.month
            ):
                sim_monthly_status.is_first_activation = False
                self.sim_repository.add_sim_monthly_status(sim_monthly_status)
            else:
                self.sim_repository.update_sim_monthly_status(
                    sim_monthly_status.sim_card_id,
                    sim_monthly_status.month,
                    SimStatus(sim_status),
                )
        else:
            if sim_status == model.SimStatus.ACTIVE:
                # New Billing logic while allocation we set flag true else false always
                sim_monthly_status.is_first_activation = False
                # New Billing logic while allocation we set flag true else false always
                self.sim_repository.add_sim_monthly_status(sim_monthly_status)

    def _mapping_carrier_name(self, marketShareUageList, carrierName):
        carrier_mapping = {
            carrier_obj.carrier: carrier_obj.carrier_name for carrier_obj in carrierName
        }
        for item in marketShareUageList:
            carrier_code = item.carrier
            carrier_name = carrier_mapping.get(carrier_code)
            if carrier_name is not None:
                item.carrier = carrier_name
            else:
                item.carrier = carrier_code
        return marketShareUageList

    def _merge_carrier_usage(self, mapped_list):
        usage_totals = {}
        for item in mapped_list:
            carrier = item.carrier
            usage = item.usage
            if carrier in usage_totals:
                usage_totals[carrier] += usage
            else:
                usage_totals[carrier] = usage
        return [
            MarketShareUsage(carrier=key, usage=value)
            for key, value in usage_totals.items()
        ]

    def update_sim_card_based_on_response(
        self,
        sim_provider_log: SIMCardProviderAudit,
        provider_log,
        sim_status: SimStatus,
        given_status: NotificationStatus,
        given_request_type: RequestType,
        client_ip: str,
        created_by: str | None = None,
    ) -> bool:
        if (given_status == NotificationStatus.SUCCESS) and (
            provider_log.requestType.upper() == given_request_type.name
        ):
            self.sim_repository.update_sim_card(
                IMSI(provider_log.imsi), SimStatus(sim_status)
            )
            sim_provider_log.status = SimStatus(sim_status)
            self._add_sim_monthly_status(
                self._get_sim_monthly_status(IMSI(provider_log.imsi), sim_provider_log),
                SimStatus(sim_status),
            )
        elif (
            given_status == NotificationStatus.FAILURE
            or given_status == NotificationStatus.FAILED
        ) and (provider_log.requestType.upper() == given_request_type.name):
            self.sim_repository.update_sim_card(
                IMSI(provider_log.imsi), SimStatus(provider_log.priorStatus)
            )
            sim_card = self.get_sim_card(IMSI(provider_log.imsi))
            iccid = sim_card.iccid
            msisdn = sim_card.msisdn

            audit_detail = model.SIMCardAudit(
                uuid4(),
                IMSI(provider_log.imsi),
                ICCID(iccid),
                MSISDN(msisdn),
                given_request_type.name,
                SimStatus(sim_status),
                SimStatus(provider_log.priorStatus),
                field="Status",
                action="Updated",
                client_ip=client_ip,
                created_by=created_by,
                audit_date=datetime.datetime.today(),
            )
            self.audit_service.add_sim_audit_api([audit_detail])

        return True

    def update_sim_card_by_imsi(
        self,
        imsi: IMSI,
        client_ip: str,
        created_by: str | None = None,
    ) -> bool:
        """Function to get SIM work id status and update by IMSI"""

        provider_id = self.sim_repository.get_provider_id(imsi)
        if provider_id == 0 or provider_id is None:
            raise exceptions.WorkItemIdNotFound(f"No request found with imsi {imsi}")

        work_id = self.audit_service.get_sim_pending_info_audit_api(imsi)
        if work_id == 0 or work_id is None:
            raise exceptions.WorkItemIdNotFound(f"No request found with imsi {imsi}")

        ppl_response = self.provisioning.sim_workitem_status(work_id)
        provider_log = self.audit_service.get_sim_provider_log_audit_api(work_id)

        sim_status = {
            RequestType.CEASE: SimStatus.DEACTIVATED,
            RequestType.PROVIDE: SimStatus.ACTIVE,
        }.get(ppl_response.request_type)

        audit_provider_detail = model.SIMCardProviderAudit(
            activity_id=str(provider_log.activityId),
            sim_activity_log_uuid=UUID(provider_log.simActivityLogUuid),
            audit_date=ppl_response.audit_date,
            message=ppl_response.message,
            status=f"{sim_status}",
            work_id=work_id,
            prior_status=provider_log.status,
        )

        if ppl_response.status != NotificationStatus.SUCCESS:
            audit_provider_detail.status = ppl_response.status

        self.audit_service.add_sim_provider_audit_api(audit_provider_detail)

        return self.update_sim_card_based_on_response(
            audit_provider_detail,
            provider_log,
            SimStatus(sim_status),
            ppl_response.status,
            ppl_response.request_type,
            client_ip,
            created_by,
        )

    def bulk_background_process(
        self,
        imsi_list: list[IMSI],
        created_by: str,
        sim_status: model.SimStatus,
        client_ip: str | None = None,
    ) -> None:
        for imsi in imsi_list:
            try:
                if sim_status == model.SimStatus.ACTIVE:
                    self.activate_sim(imsi, created_by, client_ip)
                else:
                    self.suspend_sim(imsi, created_by, client_ip)
            except exceptions.SimCardsNotFound as e:
                logger.critical(f"IMSI:{imsi}, message:{e}")
                continue
            except exceptions.SimActivationError as e:
                logger.critical(f"IMSI:{imsi}, activation:{e}")
                continue
            except exceptions.SimDeActivationError as e:
                logger.critical(f"IMSI:{imsi}, deactivation:{e}")
                continue
            except Exception as e:
                logger.error(f"IMSI:{imsi}, message:{e}")
                raise exceptions.BulkProcessingError("Could not process the request")

    def copy_monthly_statistics(self) -> int:
        copied_data = self.sim_repository.copy_monthly_statistics()
        total_rows_affected = self.sim_repository.add_bulk_sim_monthly_statistics(
            list(copied_data)
        )
        return total_rows_affected

    def get_imsis(
        self,
        iccids: list[ICCID],
    ) -> Iterator[model.IMSIDetails]:
        """Function for get SIM based on ICCID"""
        yield from self.sim_repository.get_imsis(iccids)

    def get_market_share_by_account(
        self, market_share_data: MarketShareData
    ) -> model.MarketShareCarrier:
        carrier_name = self.sim_repository.get_carrier_name()
        carrier_name_list = list(carrier_name)

        account_id = market_share_data.account_id
        result_data = self.sim_repository.get_market_share_by_account(account_id)
        result_list = list(result_data)
        if not result_list:
            raise NotFound(f"IMSI Not found with account id {account_id}")
        request_imsis = [IMSI(row.imsi) for row in result_list]
        request_data = MarketShareModel(
            imsis=request_imsis,
            from_date=market_share_data.from_date,
            to_date=market_share_data.to_date,
        )
        marketShareUsage = self.market_share_api.market_share_usage(request_data)
        if not marketShareUsage.summary:
            raise NotFound("Response is received but no summary data found")
        mapped_list = self._mapping_carrier_name(
            marketShareUsage.summary, carrier_name_list
        )
        merged_list = self._merge_carrier_usage(mapped_list)
        merged_list = sorted(merged_list, key=attrgetter("usage"), reverse=True)

        total_account_usage = sum(int(usage.usage) for usage in merged_list)
        marketShareUsage.summary = merged_list
        marketShareUsage.totalUsage = total_account_usage
        return marketShareUsage

    def get_market_share(self, period: MarketSharePeriod) -> model.MarketShareCarrier:
        carrier_name = self.sim_repository.get_carrier_name()
        carrier_name_list = list(carrier_name)
        result_data = self.sim_repository.get_market_share()
        result_list = list(result_data)
        if not result_list:
            raise NotFound("IMSI Not found with any account id")
        request_imsis = [IMSI(row.imsi) for row in result_list]
        request_data = MarketShareModel(
            imsis=request_imsis, from_date=period.from_date, to_date=period.to_date
        )
        marketShareUsage = self.market_share_api.market_share_usage(request_data)
        mapped_list = self._mapping_carrier_name(
            marketShareUsage.summary, carrier_name_list
        )
        merged_list = self._merge_carrier_usage(mapped_list)
        merged_list = sorted(merged_list, key=attrgetter("usage"), reverse=True)

        marketShareUsage.summary = merged_list
        return marketShareUsage

    def voice_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimVoiceCDRHistory]:

        connection_history = self.cdr_repository.voice_connection_history(
            imsi=imsi,
            month=month,
        )
        converted_history = (
            model.SimVoiceCDRHistory(**item.__dict__) for item in connection_history
        )
        return converted_history

    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimVoiceCDRHistory], int]:
        """Function for voice connection history"""

        response = self.check_imsi_account(account_id=account_id, imsi=[imsi])
        if response:
            connection_history = self.cdr_repository.voice_connection_history(
                imsi=imsi,
                month=month,
                pagination=pagination,
                searching=searching,
            )
            converted_history = (
                model.SimVoiceCDRHistory(**item.__dict__) for item in connection_history
            )
            total = self.cdr_repository.voice_connection_history_count(
                imsi=imsi,
                month=month,
                searching=searching,
            )
            if total == 0:
                raise exceptions.NoConnectionHistory(imsi, month)
            return converted_history, total
        else:
            raise exceptions.IMSIDoesNotExit(imsi)

    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimSMSCDRHistory], int]:
        """Function for sms connection history"""

        response = self.check_imsi_account(account_id=account_id, imsi=[imsi])
        if response:
            connection_history = self.cdr_repository.sms_connection_history(
                imsi=imsi,
                month=month,
                pagination=pagination,
                searching=searching,
            )
            converted_history = (
                model.SimSMSCDRHistory(**item.__dict__) for item in connection_history
            )
            total = self.cdr_repository.sms_connection_history_count(
                imsi=imsi,
                month=month,
                searching=searching,
            )
            if total == 0:
                raise exceptions.NoConnectionHistory(imsi, month)
            return converted_history, total
        else:
            raise exceptions.IMSIDoesNotExit(imsi)

    def sms_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimSMSCDRHistory]:

        connection_history = self.cdr_repository.sms_connection_history(
            imsi=imsi,
            month=month,
        )
        converted_history = (
            model.SimSMSCDRHistory(**item.__dict__) for item in connection_history
        )
        return converted_history

    def get_market_share_imsi(
        self, period: MarketSharePeriod, imsi: IMSI
    ) -> model.MarketShareCarrier:
        carrier_name = self.sim_repository.get_carrier_name()
        carrier_name_list = list(carrier_name)
        request_data = MarketShareModel(
            imsis=[imsi], from_date=period.from_date, to_date=period.to_date
        )
        marketShareUsage = self.market_share_api.market_share_usage(request_data)
        mapped_list = self._mapping_carrier_name(
            marketShareUsage.summary, carrier_name_list
        )
        merged_list = self._merge_carrier_usage(mapped_list)
        merged_list = sorted(merged_list, key=attrgetter("usage"), reverse=True)

        total_imsi_usage = sum(int(usage.usage) for usage in merged_list)
        marketShareUsage.summary = merged_list
        marketShareUsage.totalUsage = total_imsi_usage
        return marketShareUsage

    def _validate_imsi_allocation(
        self,
        form_data: UploadCustomIMSI,
        account_id: int,
        rate_plan_id: int,
        file_name: str,
        file: BinaryIO,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
    ) -> tuple[model.AllocationResult, list[IMSI]]:
        if (form_data.msisdn_factor == model.MSISDNFactor.INTERNATIONAL) and (
            form_data.sim_profile == model.SimProfile.VOICE_SMS_DATA
        ):
            raise ValueError(
                "sim_profile cannot be VOICE_SMS_DATA when "
                "msisdn_factor is INTERNATIONAL."
            )

        rate_plan = self.rate_plan_repository.get(rate_plan_id)

        if not rate_plan:
            raise exceptions.RatePlanNotFound(
                f"Rate Plan with id:{rate_plan_id} does not exist."
            )

        if rate_plan.account_id != account_id:
            raise exceptions.RatePlanAccountMappingError(
                "Incorrect account for the specified Rate Plan."
            )
        imsi_list = parser_impl(file=file, delimiter=";")
        sorted_sim_imsi = list(sorted(filter(None, imsi_list)))  # type: ignore
        if not sorted_sim_imsi:
            raise exceptions.IMSINotAvailableForAllocation(
                f"IMSI not available for allocation,in file {file_name}"
            )
        file_parser = FileNameParser()
        form_factor_code = file_parser._parse_file_name(file_name=file_name, file=file)

        total_sim, total_error_sim, error_list, valid_imsi = self.imsi_wise_error(
            imsi_list=sorted_sim_imsi, form_code=form_factor_code
        )

        rate_plan_sim_count = self.sim_repository.get_allocation_count_by_rate_plan(
            id=rate_plan.id
        )
        valid_sim_count = len(valid_imsi)
        aggregated_sim_count = rate_plan_sim_count + valid_sim_count

        self._check_sim_limit(
            sim_count=aggregated_sim_count,
            rate_plan_sim_count=rate_plan_sim_count,
            sim_limit=rate_plan.sim_limit,
        )

        allocation_result = model.AllocationResult(
            totalSIM=total_sim,
            errorSIM=total_error_sim,
            results=error_list,
        )
        return allocation_result, valid_imsi

    # Utility function
    def get_form_factor_code_from_string(self, form_factor_str: str) -> str | None:
        """
        Given a form factor name string like 'STANDARD' or 'Mff2',
        returns the corresponding FormFactorCode value like '2FF' or 'MFF2'.
        """
        # Reverse mapping from FormFactor to FormFactorCode
        reverse_form_factor_mapping = {v: k for k, v in form_factor_mapping.items()}
        try:
            form_factor_enum = FormFactor[form_factor_str]
            return reverse_form_factor_mapping[form_factor_enum].value
        except KeyError:
            raise exceptions.InvalidFormFactor(
                f"Invalid Form Factor: {form_factor_str}"
            )

    def custom_imsi_allocation(
        self,
        title: str,
        rate_plan_id: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        file_name: str,
        file: BinaryIO,
        client_ip: str,
        allocation_result: model.AllocationResult,
        streaming_service: AbstractKafkaAPI,
        trace_id: UUID,
        source_endpoint: str,
        account_service: AbstractAccountService,
        redis_service: AbstractRedisService,
        valid_imsi: list[IMSI],
        account_id: int,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
        created_by: str | None = None,
    ) -> model.AllocationResult:
        imsi_list = parser_impl(file=file, delimiter=";")
        sorted_sim_imsi = list(sorted(filter(None, imsi_list)))  # type: ignore

        total_valid_sim = allocation_result.totalSIM - allocation_result.errorSIM
        allocation_details = model.AllocationDetails(
            file_name=file_name,
            total_sim=total_valid_sim,
            error_sim=allocation_result.errorSIM,
        )

        available_msisdns = self.sim_repository.get_available_msisdn(
            valid_imsi, msisdn_factor
        )
        same_factor_allocation = len(available_msisdns) >= len(valid_imsi)

        free_msisdns = []
        sim_imsi_lookup = {}
        valid_imsi_set = set()

        if not same_factor_allocation:
            valid_imsi_set = set(valid_imsi)
            free_msisdns = self.sim_repository.get_msisdn_factor(
                msisdn_factor=msisdn_factor, free_count=total_valid_sim
            )
            valid_imsi = valid_imsi[: len(free_msisdns)]

            current_sim_details = list(
                self.sim_repository.get_sim_cards(imsi_list=valid_imsi)
            )
            sim_imsi_lookup = {sim.imsi: sim.msisdn for sim in current_sim_details}
            logger.info(
                f"Total {len(valid_imsi)}/{len(sorted_sim_imsi)} "
                "eligible for allocation."
            )

        file_parser = FileNameParser()
        form_factor = file_parser._parse_file_name(file_name=file_name, file=file)
        form_factor_code = self.get_form_factor_code_from_string(
            form_factor_str=form_factor
        )

        next_value = f"{len(valid_imsi)} ({form_factor_code})"
        account_log = model.AccountActivityLog(
            account_id=str(account_id),
            account_name="",
            user=created_by,  # type: ignore
            prior_value="Warehouse",
            new_value=next_value,
            prior_text="Warehouse",
            new_text=next_value,
            field="SIM",
            action="Allocation",
            client_ip=client_ip,
        )
        self.audit_service.add_account_audit_log(account_log)

        allocated_imsi_list = []
        if valid_imsi:
            allocation_list = list(
                self.sim_repository.allocation_data(
                    valid_imsi, account_id, rate_plan_id, title, created_by
                )
            )

            self.sim_repository.add_allocation_list(
                allocation_list=allocation_list,
                allocation_details=allocation_details,
                imsi_list=valid_imsi,
            )

            self.sim_repository.bulk_update_msisdn_pool(
                imsis=valid_imsi,
                msisdns=free_msisdns,
                sim_profile=sim_profile,
                msisdn_factor=msisdn_factor,
                same_factor_allocation=same_factor_allocation,
            )

            allocated_imsi_list = list(
                self.sim_repository.get_allocated_imsi(imsi_list=valid_imsi)
            )

            range_counts = Counter(
                allocation.range_id for allocation in allocation_list
            )
            for range_id, count in range_counts.items():
                self.sim_repository.update_ranges(range_id, count)

        if not same_factor_allocation and valid_imsi_set:
            error_sims = list(valid_imsi_set - set(valid_imsi))
            if error_sims:
                allocation_result.errorSIM += len(error_sims)
                allocation_result.results.extend(  # type: ignore
                    {"SIM": imsi, "Reason": "MSISDN Type not available."}
                    for imsi in error_sims
                )

        self.add_upload_file_status(
            trace_id=trace_id,
            field=ALLOCATED,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            success_count=len(valid_imsi),
            failure_count=allocation_result.errorSIM,
            uploaded_by=created_by,
            error_results=allocation_result.results,
            account_service=account_service,
            account_id=account_id,
        )

        if valid_imsi and allocated_imsi_list:
            self._create_allocation_audit_records(
                allocated_imsi_list=allocated_imsi_list,
                account_id=account_id,
                client_ip=client_ip,
                created_by=created_by,
                streaming_service=streaming_service,
                same_factor_allocation=same_factor_allocation,
                sim_imsi_lookup=sim_imsi_lookup,
            )

        redis_service.allocate_sims(account_id=account_id, rate_plan_id=rate_plan_id)

        return allocation_result

    def _create_allocation_audit_records(
        self,
        allocated_imsi_list: list,
        account_id: int,
        client_ip: str,
        same_factor_allocation: bool,
        sim_imsi_lookup: dict,
        streaming_service: AbstractKafkaAPI,
        created_by: str | None = None,
    ) -> bool:
        """Create and submit audit records for SIM allocation."""
        audit_details = []
        logger.info(f"Auditing {len(allocated_imsi_list)} records.")

        for reallocate in allocated_imsi_list:
            # Add account allocation audit
            audit_details.append(
                model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type=ALLOCATED,
                    prior_value=WAREHOUSE,
                    new_value=str(account_id),
                    field="Account",
                    action=ALLOCATED,
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
            )
            if not same_factor_allocation:
                prior_value = sim_imsi_lookup.get(reallocate.imsi)
                audit_details.append(
                    model.SIMCardAudit(
                        uuid=uuid4(),
                        imsi=IMSI(reallocate.imsi),
                        iccid=ICCID(reallocate.iccid),
                        msisdn=MSISDN(reallocate.msisdn),
                        request_type="Update MSISDN",
                        prior_value=prior_value,
                        new_value=MSISDN(reallocate.msisdn),
                        field=MSISDN_FIELD,
                        action="Updated",
                        client_ip=client_ip,
                        created_by=EmailStr(created_by),
                    )
                )

        streaming_service.submit_to_kafka(
            key=ALLOCATED,
            topic=settings.ALLOCATION_TOPIC,
            audit_details=audit_details,
        )
        return True

    def check_sim_type_available(
        self, form_factor
    ) -> Iterable[tuple[str, FormFactor, int]]:
        available_sim_type = list(self.get_sim_remains())
        form_factor_exist = [
            (item[1], item[2]) for item in available_sim_type if item[1] == form_factor
        ]
        if not form_factor_exist:
            raise exceptions.SimTypeNotAvailable(
                f"Sim type {form_factor} not available for allocation"
            )

        return available_sim_type

    def imsi_wise_error(self, imsi_list, form_code):
        invalid_imsi = list(
            filter(
                lambda imsi: not (IMSI.min_length <= len(imsi) <= IMSI.max_length),
                imsi_list,
            )
        )
        valid_imsi = list(
            filter(
                lambda imsi: IMSI.min_length <= len(imsi) <= IMSI.max_length, imsi_list
            )
        )
        imsi_counts = Counter(valid_imsi)
        duplicate_imsi = []
        for imsi, count in imsi_counts.items():
            duplicate_imsi.extend([imsi] * (count - 1))
        all_imsi_obj = list(self.sim_repository.get_all_imsi(imsi_list=valid_imsi))
        not_available_imsi = list(
            set(valid_imsi)
            - set(list(map(lambda custom_model: custom_model.imsi, all_imsi_obj)))
        )
        already_allocated_imsi = dict(
            map(
                lambda custom_model: (custom_model.imsi, custom_model.account_name),
                filter(
                    lambda custom_model: custom_model.allocation_id is not None
                    and custom_model.form_factor == form_code,
                    all_imsi_obj,
                ),
            )
        )

        not_sim_type = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.form_factor != form_code,
                    all_imsi_obj,
                ),
            )
        )
        sim_ready_for_allocation = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is None
                    and custom_model.form_factor == form_code,
                    all_imsi_obj,
                ),
            )
        )
        combined_list = []
        if invalid_imsi:
            for sim in invalid_imsi:
                combined_list.append({"SIM": sim, "Reason": "Invalid IMSI format"})
        if not_available_imsi:
            for sim in not_available_imsi:
                combined_list.append({"SIM": sim, "Reason": "Not found in IMSI Ranges"})
        if already_allocated_imsi:
            for sim, account_name in already_allocated_imsi.items():
                combined_list.append(
                    {
                        "SIM": sim,
                        "Reason": f"Already allocated to {account_name} Account",
                    }
                )
        if not_sim_type:
            for sim in not_sim_type:
                combined_list.append(
                    {
                        "SIM": sim,
                        "Reason": f"IMSI does not belong {form_code} sim type",
                    }
                )
        if duplicate_imsi:
            for sim in duplicate_imsi:
                combined_list.append(
                    {
                        "SIM": sim,
                        "Reason": "Duplicate IMSI",
                    }
                )
        total_sim = len(imsi_list)
        error_sim = len(combined_list)
        return total_sim, error_sim, combined_list, list(sim_ready_for_allocation)

    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> bool:
        response = self.sim_repository.check_imsi_account(
            account_id=account_id, imsi=imsi
        )
        if response:
            if (account_id is None) or (account_id == response):
                return True
            else:
                return False
        else:
            return False

    def re_allocation_validation(
        self,
        imsi_list: list[IMSI],
        account_id: int,
        rate_plan_id: int,
    ) -> tuple[model.ReAllocationResult, list[int]]:
        rate_plan = self.rate_plan_repository.get(rate_plan_id)

        if not rate_plan:
            raise exceptions.RatePlanNotFound(
                f"Rate Plan with id:{rate_plan_id} does not exist."
            )
        if rate_plan.account_id != account_id:
            raise exceptions.RatePlanAccountMappingError(
                "Incorrect account for the specified Rate Plan."
            )

        if len(imsi_list) != len(set(imsi_list)):
            raise exceptions.AllocationError("Duplicate IMSI selected")

        allocated_imsi_list = [
            sim_card
            for sim_card in self.sim_repository.get_allocated_imsi(imsi_list=imsi_list)
        ]
        account_ids_list = [allocation.account_id for allocation in allocated_imsi_list]
        if len(account_ids_list) != len(imsi_list):
            raise exceptions.IMSINotAvailableForAllocation(
                "IMSI not available for re_allocation"
            )
        if len(set(account_ids_list)) > 1:
            raise exceptions.IMSINotBelongToSameAccount(
                "IMSI should not belong to same account"
            )

        valid_imsis = self._get_valid_reallocation_imsi(
            sim_cards=allocated_imsi_list,
            rate_plan_id=rate_plan_id,
            account_id=account_id,
        )
        if valid_imsis.validSIM:
            rate_plan_sim_count = self.sim_repository.get_allocation_count_by_rate_plan(
                id=rate_plan.id
            )
            aggregated_sim_count = rate_plan_sim_count + valid_imsis.validSIM

            self._check_sim_limit(
                sim_count=aggregated_sim_count,
                rate_plan_sim_count=rate_plan_sim_count,
                sim_limit=rate_plan.sim_limit,
            )
        rp_ids = [rp_id.rate_plan_id for rp_id in allocated_imsi_list]
        return valid_imsis, rp_ids  # type: ignore

    def _get_valid_reallocation_imsi(
        self, sim_cards, rate_plan_id: int, account_id: int
    ) -> model.ReAllocationResult:

        valid_reallocation_imsi_list = []
        same_account_valid_imsi_list = []
        for sim_card in sim_cards:
            sim_card_imsi = sim_card.imsi
            if sim_card.rate_plan_id != rate_plan_id:
                valid_reallocation_imsi_list.append(sim_card_imsi)
            if sim_card.account_id == account_id:
                same_account_valid_imsi_list.append(sim_card_imsi)

        validSIM = len(valid_reallocation_imsi_list)
        errorSIM = len(sim_cards) - validSIM

        message = (
            f"{validSIM} IMSIs will be re-allocated and"
            f" {errorSIM} IMSIs already assigned to choosen Rate Plan."
        )
        logger.debug(message)

        if not valid_reallocation_imsi_list:
            raise exceptions.ReAllocationError(message)
        return model.ReAllocationResult(
            validSIM=validSIM,
            errorSIM=errorSIM,
            message=message,
            valid_imsi_list=valid_reallocation_imsi_list,
            same_account_imsi_list=same_account_valid_imsi_list,
        )

    def re_allocation(
        self,
        account_id: int,
        rate_plan_id: int,
        imsi_list: list[IMSI],
        client_ip: str,
        created_by: str,
        same_account_imsi_list: list[IMSI] | None = None,
    ) -> bool:
        logger.debug(
            f"Re_allocation_Re-allocation of IMSI {imsi_list} "
            f"for account {account_id} and rate plan {rate_plan_id}"
        )

        reallocated_imsi_list = [
            sim_card
            for sim_card in self.sim_repository.get_allocated_imsi(imsi_list=imsi_list)
        ]
        audit_details = []

        for reallocate in reallocated_imsi_list:
            imsi, iccid, msisdn = reallocate.imsi, reallocate.iccid, reallocate.msisdn
            # Create audit details based on account or rate plan changes
            if reallocate.imsi in (same_account_imsi_list or []):
                audit_details.append(
                    model.SIMCardAudit(
                        uuid=uuid4(),
                        imsi=IMSI(imsi),
                        iccid=ICCID(iccid),
                        msisdn=MSISDN(msisdn),
                        request_type="Updated",
                        prior_value=str(reallocate.rate_plan_id),
                        new_value=str(rate_plan_id),
                        field="Rate Plan",
                        action="Updated",
                        client_ip=client_ip,
                        created_by=EmailStr(created_by),
                    )
                )
            else:
                audit_details.extend(
                    [
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(imsi),
                            iccid=ICCID(iccid),
                            msisdn=MSISDN(msisdn),
                            request_type="Re-allocated",
                            prior_value=str(reallocate.account_id),
                            new_value=str(account_id),
                            field="Account",
                            action="Re-allocated",
                            client_ip=client_ip,
                            created_by=EmailStr(created_by),
                        ),
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(imsi),
                            iccid=ICCID(iccid),
                            msisdn=MSISDN(msisdn),
                            request_type="Re-allocated",
                            prior_value=str(reallocate.rate_plan_id),
                            new_value=str(rate_plan_id),
                            field="Rate Plan",
                            action="Re-allocated",
                            client_ip=client_ip,
                            created_by=EmailStr(created_by),
                        ),
                    ]
                )

        self.audit_service.add_allocation_audit_api(audit_details)

        if reallocate.imsi not in (same_account_imsi_list or []):
            # Group by previous account and form factor for account activity logs
            account_form_factor_counts = defaultdict(
                lambda: defaultdict(int)
            )  # type: ignore
            reverse_form_factor_mapping = {
                v: k.value for k, v in form_factor_mapping.items()
            }

            for reallocate in reallocated_imsi_list:
                try:
                    ff_code = reverse_form_factor_mapping[
                        reallocate.form_factor  # type: ignore
                    ]
                except KeyError:
                    raise ValueError(f"Invalid form factor: {reallocate.form_factor}")

                # Group by previous account_id and form factor
                previous_account_id = reallocate.account_id
                account_form_factor_counts[previous_account_id][ff_code] += 1

            # Create account activity logs for each previous account + form factor
            for (
                previous_account_id,
                form_factor_counts,
            ) in account_form_factor_counts.items():
                for ff_code, count in form_factor_counts.items():
                    next_value = f"{account_id} ({ff_code})"
                    account_audit = model.AccountActivityLog(
                        account_id=str(account_id),
                        account_name="",
                        user=created_by,
                        prior_value=str(previous_account_id),
                        new_value=next_value,
                        prior_text=str(previous_account_id),
                        new_text=next_value,
                        field="SIM",
                        action="Re-allocation",
                        client_ip=client_ip,
                    )
                    self.audit_service.add_account_audit_log(account_audit)
        try:
            response = self.sim_repository.imsi_reallocation_func(
                account_id=account_id,
                rate_plan_id=rate_plan_id,
                imsis=imsi_list,
            )

            if response != "Success":
                self._roll_back_reallocation_audit(
                    rate_plan_id=rate_plan_id,
                    reallocated_imsi_list=reallocated_imsi_list,
                    account_id=account_id,
                    client_ip=client_ip,
                    created_by=created_by,
                    same_account_imsi_list=same_account_imsi_list,
                )
                logger.error(f"Re-allocation IMSI error - {str(response)}")
        except IntegrityError as e:
            logger.error(f"Imsi reallocation error.: {str(e)}")
            self._roll_back_reallocation_audit(
                rate_plan_id=rate_plan_id,
                reallocated_imsi_list=reallocated_imsi_list,
                account_id=account_id,
                client_ip=client_ip,
                created_by=created_by,
                same_account_imsi_list=same_account_imsi_list,
            )
            raise exceptions.IMSIWiseError(f"Imsi reallocation error.: {str(e)}")
        return True

    def _roll_back_reallocation_audit(
        self,
        rate_plan_id: int,
        reallocated_imsi_list: list[model.Allocation],
        account_id: int,
        client_ip: str,
        created_by: str,
        same_account_imsi_list: list[IMSI] | None = None,
    ) -> None:
        audit_data = []
        for reallocate in reallocated_imsi_list:
            if reallocate.imsi in same_account_imsi_list:  # type: ignore
                audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type="Re-allocated",
                    prior_value=str(reallocate.rate_plan_id),
                    new_value=str(rate_plan_id),
                    field="Rate Plan",
                    action="Failed",
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
                audit_data.append(audit_detail)
            else:
                audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type="Re-allocated",
                    prior_value=str(reallocate.account_id),
                    new_value=str(account_id),
                    field="Account",
                    action="Failed",
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
                audit_data.append(audit_detail)

                rate_plan_audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type="Re-allocated",
                    prior_value=str(reallocate.rate_plan_id),
                    new_value=str(rate_plan_id),
                    field="Rate Plan",
                    action="Failed",
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
                audit_data.append(rate_plan_audit_detail)

        self.audit_service.add_allocation_audit_api(audit_data)

    def update_msisdn(self, excel_data) -> bool:
        for _, row in excel_data.iterrows():
            imsi = row["IMSI"]
            msisdn = row["MSISDN"]
            if self.sim_repository.is_msisdn_already_exists(msisdn):
                raise exceptions.MSISDNExitError(
                    "MSISDN is already assigned to another SIM card."
                )
            if self.sim_repository.is_imsi_exists(imsi):
                raise exceptions.NoSimFound()
            self.sim_repository.update_sim_msisdn_by_imsi(imsi, msisdn_type(msisdn))
        return True

    def rate_plan_change_sim_validation(
        self,
        imsi: IMSI,
        account_id: int,
        rate_plan_id: int,
    ) -> model.RatePlanChangeSimLimitResult:

        try:
            new_rate_plan_info = (
                self.sim_repository.get_rate_plan_with_allocation_count(rate_plan_id)
            )

            if not new_rate_plan_info:
                raise exceptions.RatePlanNotFound(
                    f"Rate Plan with id: {rate_plan_id} does not exist."
                )
            if new_rate_plan_info.account_id != account_id:
                raise exceptions.RatePlanAccountMappingError(
                    "Incorrect account for the specified Rate Plan."
                )

            imsi_rate_plan_id = self.sim_repository.get_rate_plan_by_imsi(imsi=imsi)

            if not imsi_rate_plan_id:
                raise exceptions.SimCardsNotFound("SIM not found.")

            if imsi_rate_plan_id == rate_plan_id:
                raise exceptions.RatePlanException(
                    "IMSI already assigned to the specified plan."
                )

            if (
                new_rate_plan_info.sim_limit is None
                or new_rate_plan_info.allocated_sim_count < new_rate_plan_info.sim_limit
            ):
                message = "Rate Plan will be changed"
            else:
                raise exceptions.RatePlanChangeNotAllowed("Rate Plan can't be changed")

            return model.RatePlanChangeSimLimitResult(message=message)
        except Exception:
            raise

    def get_msisdn_factor(
        self,
        msisdn_factor: model.MSISDNFactor,
    ) -> str:
        """Function to get Random MSISDN based on SIM Profile"""
        msisdn = self.sim_repository.get_msisdn_factor(msisdn_factor)
        if not msisdn:
            raise exceptions.NotFound(
                f"No free MSISDN found with factor {msisdn_factor}"
            )
        return msisdn[0]

    def _validate_sim_card_details_by_imsi(
        self,
        existing_msisdn: MSISDN,
        existing_imsi: IMSI,
        imsi: IMSI,
        msisdn: msisdn_type,
        sim_profile: model.SimProfile,
        existing_profile: model.SimProfile,
    ) -> None:
        if not existing_msisdn:
            logger.error(f"MSISDN Not found: {existing_msisdn}")
            raise exceptions.MSISDNNotFound("MSISDN not found.")

        if (
            imsi == existing_imsi
            and msisdn == existing_msisdn
            and sim_profile != existing_profile
        ):
            pass
        else:
            if existing_imsi:
                logger.error(
                    f"MSISDN is already associated with an IMSI: {existing_imsi}, "
                    f"MSISDN: {existing_msisdn}"
                )
                raise exceptions.AlreadyExist("Invalid MSISDN")

    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: msisdn_type,
        sim_profile: model.SimProfile,
        streaming_service: AbstractKafkaAPI,
        background_tasks: BackgroundTasks,
        client_ip: str,
        created_by: str | None = None,
    ) -> model.UpdateSimCardDetailsResult:
        """Function to update MSISDN and right after need to perform deactivate"""
        sim_card = self.get_sim_card(IMSI(imsi))
        if SimStatus(sim_card.sim_status) and (
            SimStatus(sim_card.sim_status) == SimStatus.PENDING
            or SimStatus(sim_card.sim_status) == SimStatus.ACTIVE
        ):
            logger.error(
                f"Cannot update Pending or Active State Sim. "
                f"Imsi: {imsi}, msisdn: {msisdn}, "
                f"SimStatus: {sim_card.sim_status}"
            )
            raise exceptions.IMSIError("Cannot update Pending or Active Sim.")

        (
            existing_msisdn,
            existing_allocation_id,
            existing_imsi,
            existing_profile,
            existing_imsi_msisdn,
        ) = self.sim_repository.validate_sim_card_details_by_imsi(
            imsi=imsi, msisdn=msisdn
        )

        self._validate_sim_card_details_by_imsi(
            existing_msisdn=existing_msisdn,
            existing_imsi=existing_imsi,
            existing_profile=existing_profile,
            imsi=imsi,
            msisdn=msisdn,
            sim_profile=sim_profile,
        )

        response = self.sim_repository.update_sim_card_details_by_imsi(
            imsi=imsi,
            msisdn=msisdn,
            sim_profile=sim_profile,
            allocation_id=existing_allocation_id,
            existing_msisdn=existing_imsi_msisdn,
            existing_profile=existing_profile,
        )
        if not response:
            logger.error(
                f"Unexpected error occoured: {imsi}, msisdn: {msisdn}, "
                f"SimStatus: {sim_card.sim_status}"
                f"Response: {response}"
            )
            raise exceptions.IMSIError("Unexpected error occoured.")
        # Adding audit details
        audit_detail = []
        if sim_card.msisdn != msisdn:
            audit_detail.append(
                model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(imsi),
                    iccid=ICCID(sim_card.iccid),
                    msisdn=MSISDN(msisdn),
                    request_type="Update MSISDN",
                    prior_value=MSISDN(sim_card.msisdn),
                    new_value=MSISDN(msisdn),
                    field="MSISDN",
                    action="Updated",
                    client_ip=client_ip,
                    created_by=created_by,
                )
            )

        if existing_profile != sim_profile:
            audit_detail.append(
                model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(imsi),
                    iccid=ICCID(sim_card.iccid),
                    msisdn=MSISDN(msisdn),
                    request_type="Update MSISDN",
                    prior_value=existing_profile,
                    new_value=sim_profile,
                    field="SIM Profile",
                    action="Updated",
                    client_ip=client_ip,
                    created_by=created_by,
                )
            )

        # self.audit_service.add_upload_msisdn_audit_api(audit_details=audit_detail)

        sim_card = self.get_sim_card(IMSI(imsi))
        background_tasks.add_task(
            streaming_service.submit_to_kafka,
            key="update_sim_card_details",
            topic=settings.UPLOAD_MSISDN_TOPIC,
            audit_details=audit_detail,
        )

        return model.UpdateSimCardDetailsResult(
            imsi=sim_card.imsi,
            msisdn=sim_card.msisdn,
            sim_profile=sim_profile,
        )

    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[list[model.MsisdnDetails], int]:
        total_count = self.sim_repository.pool_msisdn_count(searching=searching)
        if total_count == 0:
            raise exceptions.NotFound()
        response = list(
            self.sim_repository.get_msisdn_pool_details(
                pagination=pagination, searching=searching
            )
        )

        for upload_msisdn in response:
            upload_msisdn.logo_url = self._get_logo_url(upload_msisdn.logo_key)
        return response, total_count

    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        response = self.sim_repository.get_msisdn_export(searching=searching)
        if not response:
            raise exceptions.NotFound("No msisdn details found.")
        return response

    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:
        return self.sim_repository.get_available_msisdn_count()

    def creat_msisdn_response(
        self,
        invalid_format: list[str],
        pool_msisdn: list[MSISDN],
        sim_card_msisdn: list[MSISDN],
        duplicate_msisdn: list[MSISDN],
    ) -> model.MsisdnResult:

        results = []

        # Add invalid format errors
        for msisdn in invalid_format:
            results.append({"MSISDN": msisdn, "Reason": "Invalid MSISDN format"})

        for msisdn in duplicate_msisdn:
            results.append({"MSISDN": msisdn, "Reason": "Duplicate MSISDN"})

        for msisdn in pool_msisdn:
            results.append({"MSISDN": msisdn, "Reason": "Already uploaded"})

        # Add already allocated errors
        for msisdn in sim_card_msisdn:
            results.append({"MSISDN": msisdn, "Reason": "Already uploaded"})

        return model.MsisdnResult(
            error_msisdn=len(results),
            error_result=results if results else [],
        )

    def build_upload_status_email(self, file_details: model.UploadedFileAudit):
        status = file_details.status
        process_name = file_details.action

        keywords = ["imsi", "msisdn", "sim"]

        words = process_name.split()
        process_name = " ".join(
            [word.upper() if word.lower() in keywords else word for word in words]
        )

        process_id = file_details.request_id
        date_time = file_details.payload.created_at
        time_str = date_time.strftime("%I:%M %p")
        date_str = date_time.strftime("%Y-%m-%d")
        result_link = (
            f"{settings.APP_LANDING_PAGE_URL}"
            f"/sim-management?tab=bulk-operation&search={process_id}"
        )
        message = file_details.payload.message
        values = {
            "status": status,
            "process_name": process_name,
            "process_id": process_id,
            "time_str": time_str,
            "date_str": date_str,
            "result_link": result_link,
            "message": message,
        }
        return build_subject_and_body(values, status)

    def add_upload_file_status(
        self,
        trace_id: UUID,
        field: str,
        source_endpoint: str,
        client_ip: str,
        account_service: AbstractAccountService,
        account_id: int | None = None,
        account_name: str | None = None,
        success_count: int | None = None,
        failure_count: int | None = None,
        uploaded_by: str | None = None,
        message: str | None = None,
        error_results: list[dict] | None = None,
    ) -> bool:
        status = self._determine_upload_status(success_count, failure_count, message)
        logger.info(f" add_upload_file_status Account ID: {account_id}")
        if account_id:
            account_name = account_service.get(account_id).name
        file_details = self._create_file_audit(
            trace_id,
            uploaded_by,
            client_ip,
            source_endpoint,
            field,
            status,
            message,
            success_count,
            failure_count,
            error_results,
            account_id,
            account_name,
        )

        self.audit_service.add_uploaded_file_status_audit_api(file_details)
        logger.info("add_upload_file_status added into audit")
        self._send_status_email(file_details)
        return True

    def _determine_upload_status(
        self, success_count: int | None, failure_count: int | None, message: str | None
    ) -> str:
        if not success_count and not failure_count:
            return (
                UploadFileStatus.PENDING
                if not message or message == UploadFileStatus.RECEIVED
                else UploadFileStatus.FAILED
            )
        return (
            UploadFileStatus.COMPLETED
            if not failure_count
            else UploadFileStatus.FAILED
            if not success_count
            else UploadFileStatus.PARTIALLY
        )

    def _create_file_audit(
        self,
        trace_id: UUID,
        uploaded_by: str | None,
        client_ip: str,
        source_endpoint: str,
        field: str,
        status: str,
        message: str | None,
        success_count: int | None,
        failure_count: int | None,
        error_results: list[dict] | None,
        account_id: int | None = None,
        account_name: str | None = None,
    ) -> model.UploadedFileAudit:
        payload_message = (
            message
            if status == UploadFileStatus.FAILED
            and not success_count
            and not failure_count
            else (
                UploadFileStatus.FAILED
                if status == UploadFileStatus.FAILED
                else (message if message else UploadFileStatus.RECEIVED)
            )
        )
        if payload_message is None:
            payload_message = UploadFileStatus.RECEIVED
        return model.UploadedFileAudit(
            request_id=trace_id,
            user=uploaded_by,
            client_ip=client_ip,
            action=source_endpoint,
            field=field,
            status=status,
            account_id=account_id,
            account_name=account_name,
            payload=model.UploadedFileStatus(
                source_endpoint=source_endpoint.replace("_", " ").title(),
                message=payload_message,
                success_count=success_count if success_count else 0,
                failure_count=failure_count if failure_count else 0,
                created_at=datetime.datetime.today(),
                error_results=error_results if error_results else None,
            ),
        )

    def _send_status_email(self, file_details: model.UploadedFileAudit) -> None:
        if not file_details.user:
            return
        try:
            subject, body = self.build_upload_status_email(file_details=file_details)
            self.mail_service.send_mail(
                subject=subject,
                name_from="BT IoT Portal",
                recipients=[file_details.user],
                html_body=body,
            )
        except Exception as exc:
            logger.error(f"Exception during mail send: {exc}")

    def upload_msisdn(
        self,
        msisdn_list: list[MSISDN],
        invalid_format: list[str],
        duplicate_msisdn: list[MSISDN],
        client_ip: str,
        trace_id: UUID,
        source_endpoint: str,
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        uploaded_by: str | None = None,
        account_id: int | None = None,
    ) -> model.MsisdnResult:
        """
        MSISDN Factor will be set to NATIONAL default
        NATIONAL if msisdn starts with 447 or any other number
        INTERNATIONAL if msisdn starts with 883
        """
        pool_msisdns = set(
            self.sim_repository.get_msisdn_in_pool_msisdn_table(msisdn_list=msisdn_list)
        )
        logger.info(f"Pool MSISDNs -{pool_msisdns}")
        remaining_msisdn = set(msisdn_list) - pool_msisdns
        sim_card_msisdn = set(
            self.sim_repository.get_msisdn_in_sim_card_table(
                msisdn_list=remaining_msisdn
            )
        )
        logger.info(f"Sim card MSISDNs -{sim_card_msisdn}")
        audit_details = []
        if remaining_msisdn:
            created_at = datetime.datetime.today()
            msisdn_factors = list(
                map(
                    lambda m: MSISDNFactor.INTERNATIONAL
                    if m.startswith(MSISDN_883)
                    else MSISDNFactor.NATIONAL,
                    remaining_msisdn,
                )
            )
            pool_msisdn = list(
                map(
                    lambda args: model.MsisdnPool(
                        msisdn=args[0],
                        sim_profile=model.SimProfile.DATA_ONLY,
                        msisdn_factor=args[1],
                        uploaded_by=uploaded_by,
                        created_at=created_at,
                    ),
                    zip(remaining_msisdn, msisdn_factors),
                )
            )
            self.sim_repository.upload_msisdn(pool_msisdn)
            logger.info(f"MSISDN uploaded successfully:{len(pool_msisdn)}")

        result = self.creat_msisdn_response(
            invalid_format=invalid_format,
            pool_msisdn=list(pool_msisdns),
            sim_card_msisdn=list(sim_card_msisdn),
            duplicate_msisdn=duplicate_msisdn,
        )
        self.add_upload_file_status(
            trace_id=trace_id,
            field=MSISDN_FIELD,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            success_count=len(remaining_msisdn),
            failure_count=result.error_msisdn,
            uploaded_by=uploaded_by,
            error_results=result.error_result,
            account_service=account_service,
            account_id=account_id,
        )
        logger.info(f"MSISDN upload response:{result}")
        if remaining_msisdn:
            # Adding audit details
            audit_details = [
                audit_entry
                for msisdn, factor in zip(remaining_msisdn, msisdn_factors)
                for audit_entry in [
                    model.MsisdnPoolAudit(
                        uuid=uuid4(),
                        msisdn=MSISDN(msisdn),
                        request_type="Upload MSISDN",
                        prior_value=WAREHOUSE,
                        new_value=MSISDN(msisdn),
                        field=MSISDN_FIELD,
                        action="Uploaded",
                        client_ip=client_ip,
                        created_by=uploaded_by,
                    ),
                    model.MsisdnPoolAudit(
                        uuid=uuid4(),
                        msisdn=MSISDN(msisdn),
                        request_type="Upload MSISDN",
                        prior_value=WAREHOUSE,
                        new_value=model.SimProfile.DATA_ONLY.name,
                        field="SIM Profile",
                        action="Uploaded",
                        client_ip=client_ip,
                        created_by=uploaded_by,
                    ),
                    model.MsisdnPoolAudit(
                        uuid=uuid4(),
                        msisdn=MSISDN(msisdn),
                        request_type="Upload MSISDN",
                        prior_value=WAREHOUSE,
                        new_value=factor,
                        field="MSISDN Type",
                        action="Uploaded",
                        client_ip=client_ip,
                        created_by=uploaded_by,
                    ),
                ]
            ]
            streaming_service.submit_to_kafka(
                key=source_endpoint,
                topic=settings.UPLOAD_MSISDN_TOPIC,
                audit_details=audit_details,
            )

        return result

    def _validate_sim_card_details_by_imsi_msisdn(
        self,
        current_sim_details: list[model.SIMCard],
        valid_imsi_list: list[IMSI],
        duplicate_imsi: list[IMSI],
        valid_data: DataFrame,
        valid_msisdn_list: list[MSISDN],
        duplicate_msisdn: list[MSISDN],
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        invalid_records: list[dict],
    ):
        valid_data_details = valid_data.to_dict(orient="records")
        valid_data_look_up = {sim["IMSI"]: sim["MSISDN"] for sim in valid_data_details}
        valid_data_msisdn_look_up = {
            sim["MSISDN"]: sim["IMSI"] for sim in valid_data_details
        }
        not_available_imsi = list(
            set(valid_imsi_list)
            - set(
                list(map(lambda custom_model: custom_model.imsi, current_sim_details))
            )
        )

        unallocated_imsi = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is None,
                    current_sim_details,
                ),
            )
        )

        pending_imsi = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is not None
                    and SimStatus(custom_model.sim_status) == SimStatus.PENDING,
                    current_sim_details,
                ),
            )
        )

        active_imsi = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is not None
                    and SimStatus(custom_model.sim_status) == SimStatus.ACTIVE,
                    current_sim_details,
                ),
            )
        )

        updated_valid_imsi_list = list(
            set(valid_imsi_list)
            - set(duplicate_imsi)
            - set(unallocated_imsi)
            - set(pending_imsi)
            - set(active_imsi)
            - set(not_available_imsi)
        )
        updated_valid_msisdn_list = list(set(valid_msisdn_list) - set(duplicate_msisdn))
        # Filter data where IMSI is in imsi_list
        filtered_data = valid_data[valid_data["IMSI"].isin(updated_valid_imsi_list)]

        # Further filter data where MSISDN is in msisdn_list
        filtered_data = filtered_data[
            filtered_data["MSISDN"].isin(updated_valid_msisdn_list)
        ]

        # Store the final filtered records
        new_updated_records = filtered_data.to_dict(orient="records")

        valid_sim_card_details = self.sim_repository.validate_bulk_sim_card_details(
            sim_card_list=new_updated_records,
            valid_imsi_list=updated_valid_imsi_list,
            valid_msisdn_list=updated_valid_msisdn_list,
        )

        msisdn_not_found = []
        msisdn_associated_with_an_imsi = []
        msisdn_msisdn_factor_mismatch = []

        already_associated_imsi_with_msisdn = []
        for sim_details in valid_sim_card_details:
            if not sim_details.msisdn_value:
                logger.error(f"MSISDN Not found: {sim_details.requested_msisdn}")
                msisdn_not_found.append(sim_details.requested_msisdn)

            if sim_details.msisdn_value and sim_details.msisdn_factor != msisdn_factor:
                logger.error(
                    f"MSISDN Factor Mismatch: "
                    f"Existing={sim_details.msisdn_value}, "
                    f"Expected={sim_details.msisdn_factor}"
                )
                msisdn_msisdn_factor_mismatch.append(sim_details.msisdn_value)

            if (
                sim_details.existing_msisdn
                and sim_details.existing_msisdn == sim_details.requested_msisdn
                and sim_details.msisdn_sim_profile == sim_profile
                and sim_details.existing_msisdn not in msisdn_msisdn_factor_mismatch
            ):
                logger.error(
                    f"SIM is already associated with requested MSISDN: "
                    f"IMSI={sim_details.requested_imsi}, "
                    f"MSISDN={sim_details.requested_msisdn}"
                )
                already_associated_imsi_with_msisdn.append(sim_details.requested_imsi)

            if (
                sim_details.existing_imsi
                and sim_details.existing_msisdn
                and sim_details.existing_msisdn != sim_details.requested_msisdn
                and sim_details.requested_msisdn not in msisdn_msisdn_factor_mismatch
                and sim_details.requested_msisdn
                not in already_associated_imsi_with_msisdn
            ):
                logger.error(
                    f"MSISDN is already associated with an "
                    f"IMSI: {sim_details.existing_imsi}, "
                    f"MSISDN: {sim_details.requested_msisdn}"
                )
                msisdn_associated_with_an_imsi.append(sim_details.requested_msisdn)

            logger.info(
                f"Allocation ID: {sim_details.allocation_id}, "
                f"Existing MSISDN: {sim_details.existing_msisdn}"
            )

        final_valid_imsi_list = list(
            set(updated_valid_imsi_list) - set(already_associated_imsi_with_msisdn)
        )
        final_valid_msisdn_list = list(
            set(updated_valid_msisdn_list)
            - set(msisdn_not_found)
            - set(msisdn_associated_with_an_imsi)
            - set(msisdn_msisdn_factor_mismatch)
        )

        # Filter data where IMSI is in imsi_list
        final_filtered_data = valid_data[valid_data["IMSI"].isin(final_valid_imsi_list)]

        # Further filter data where MSISDN is in msisdn_list
        final_filtered_data = final_filtered_data[
            final_filtered_data["MSISDN"].isin(final_valid_msisdn_list)
        ]

        # Store the final filtered records
        final_updated_records = final_filtered_data.to_dict(orient="records")

        final_valid_sim_card_details = (
            self.sim_repository.validate_bulk_sim_card_details(
                sim_card_list=final_updated_records,
                valid_imsi_list=final_valid_imsi_list,
                valid_msisdn_list=final_valid_msisdn_list,
            )
        )

        if not final_valid_sim_card_details:
            final_valid_imsi_list = []

        error_response = []
        if not_available_imsi:
            for imsi in not_available_imsi:
                error_response.append(
                    {
                        "MSISDN": valid_data_look_up.get(imsi, None),
                        "IMSI": imsi,
                        "Reason": "IMSI not found",
                    }
                )

        if duplicate_imsi:
            for imsi in duplicate_imsi:
                error_response.append(
                    {
                        "MSISDN": valid_data_look_up.get(imsi, None),
                        "IMSI": imsi,
                        "Reason": "Duplicate IMSI found",
                    }
                )

        if unallocated_imsi:
            for imsi in unallocated_imsi:
                error_response.append(
                    {
                        "MSISDN": valid_data_look_up.get(imsi, None),
                        "IMSI": imsi,
                        "Reason": "IMSI allocation not found",
                    }
                )

        if pending_imsi:
            for imsi in pending_imsi:
                error_response.append(
                    {
                        "MSISDN": valid_data_look_up.get(imsi, None),
                        "IMSI": imsi,
                        "Reason": "The SIM cannot be updated while it is pending",
                    }
                )

        if active_imsi:
            for imsi in active_imsi:
                error_response.append(
                    {
                        "MSISDN": valid_data_look_up.get(imsi, None),
                        "IMSI": imsi,
                        "Reason": "The SIM cannot be updated while it is active",
                    }
                )

        if already_associated_imsi_with_msisdn:
            for imsi_value in already_associated_imsi_with_msisdn:
                error_response.append(
                    {
                        "MSISDN": valid_data_look_up.get(imsi_value, None),
                        "IMSI": imsi_value,
                        "Reason": "Invalid IMSI and MSISDN",
                    }
                )

        if duplicate_msisdn:
            for msisdn in duplicate_msisdn:
                error_response.append(
                    {
                        "MSISDN": msisdn,
                        "IMSI": valid_data_msisdn_look_up.get(msisdn, None),
                        "Reason": "Duplicate MSISDN found",
                    }
                )

        if msisdn_not_found:
            for msisdn in msisdn_not_found:
                error_response.append(
                    {
                        "MSISDN": msisdn,
                        "IMSI": valid_data_msisdn_look_up.get(msisdn, None),
                        "Reason": "MSISDN Not found",
                    }
                )

        if msisdn_associated_with_an_imsi:
            for msisdn in msisdn_associated_with_an_imsi:
                error_response.append(
                    {
                        "MSISDN": msisdn,
                        "IMSI": valid_data_msisdn_look_up.get(msisdn, None),
                        "Reason": "Invalid MSISDN",
                    }
                )

        if msisdn_msisdn_factor_mismatch:
            for msisdn in msisdn_msisdn_factor_mismatch:
                error_response.append(
                    {
                        "MSISDN": msisdn,
                        "IMSI": valid_data_msisdn_look_up.get(msisdn, None),
                        "Reason": "MSISDN Type Mismatch",
                    }
                )
        if invalid_records:
            for invalid_data in invalid_records:

                error_response.append(
                    {
                        "MSISDN": invalid_data.get("MSISDN"),
                        "IMSI": invalid_data.get("IMSI"),
                        "Reason": "Invalid records",
                    }
                )

        return final_valid_sim_card_details, final_valid_imsi_list, error_response

    def bulk_update_sim_card_details(
        self,
        total_records: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        invalid_records: list[dict],
        duplicate_imsi: list[IMSI],
        duplicate_msisdn: list[MSISDN],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[MSISDN],
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        client_ip: str,
        trace_id: UUID,
        source_endpoint: str,
        valid_data: DataFrame,
        uploaded_by: str | None = None,
        account_id: int | None = None,
    ) -> model.BulkSimCardUpdateResult:

        current_sim_details = self.sim_repository.get_sim_cards(
            imsi_list=valid_imsi_list
        )
        current_sim_details_list = list(current_sim_details)
        sim_imsi_lookup = {sim.imsi: sim.msisdn for sim in current_sim_details_list}
        sim_profile_lookup = {
            sim.imsi: sim.sim_profile for sim in current_sim_details_list
        }
        (
            final_valid_results,
            final_valid_imsi_list,
            error_response,
        ) = self._validate_sim_card_details_by_imsi_msisdn(
            current_sim_details=current_sim_details_list,
            valid_imsi_list=valid_imsi_list,
            duplicate_imsi=duplicate_imsi,
            valid_data=valid_data,
            valid_msisdn_list=valid_msisdn_list,
            duplicate_msisdn=duplicate_msisdn,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            invalid_records=invalid_records,
        )

        response = self.sim_repository.bulk_update_sim_card_details(
            all_sim_card_details=final_valid_results, sim_profile=sim_profile
        )

        if not response:
            logger.error(f"Response: {response}")
            raise exceptions.IMSIError("Unexpected error occoured.")

        final_valid_reponse = self.sim_repository.get_sim_cards(
            imsi_list=final_valid_imsi_list
        )

        result = model.BulkSimCardUpdateResult(
            total_details=total_records,
            error_details=len(error_response),
            error_results=error_response,
        )
        self.add_upload_file_status(
            trace_id=trace_id,
            field=MSISDN_FIELD,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            success_count=len(final_valid_imsi_list),
            failure_count=len(error_response),
            uploaded_by=uploaded_by,
            error_results=error_response,
            account_service=account_service,
            account_id=account_id,
        )
        logger.info(f"bulk update sim card details: {result}")

        audit_details = []
        if final_valid_imsi_list:
            for sim_details in final_valid_reponse:
                prior_profile = sim_profile_lookup.get(sim_details.imsi, None)
                prior_msisdn = sim_imsi_lookup.get(sim_details.imsi, None)
                # Adding audit details
                if prior_msisdn != sim_details.msisdn:
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_details.imsi),
                            iccid=ICCID(sim_details.iccid),
                            msisdn=MSISDN(sim_details.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_msisdn,
                            new_value=MSISDN(sim_details.msisdn),
                            field=MSISDN_FIELD,
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )
                if (
                    sim_details.sim_profile
                    and prior_profile != sim_details.sim_profile.name
                ):
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_details.imsi),
                            iccid=ICCID(sim_details.iccid),
                            msisdn=MSISDN(sim_details.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_profile,
                            new_value=sim_details.sim_profile,
                            field="SIM Profile",
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )

            streaming_service.submit_to_kafka(
                key=source_endpoint,
                topic=settings.UPLOAD_MSISDN_TOPIC,
                audit_details=audit_details,
            )

        return result

    @staticmethod
    def get_common_value(data: dict) -> str | None:
        if not data:
            raise ValueError("The dictionary is empty.")

        first_value = next(iter(data.values()))

        if not all(value == first_value for value in data.values()):
            return None

        return first_value

    def validate_common_request(self, msisdn_list: list[msisdn_type]) -> bool:
        all_same_type = self.sim_repository.validate_msisdn_update_request(
            msisdn_list, "msisdn_factor"
        )
        all_same_profile = self.sim_repository.validate_msisdn_update_request(
            msisdn_list, "sim_profile"
        )
        if (not all_same_type) or (not all_same_profile):
            raise exceptions.IMSIError(
                "The requested MSISDNs have different MSISDN Type or "
                "belong to different SIM Profile."
            )
        return True

    def update_sim_card_details(
        self,
        sim_profile: model.SimProfile,
        total_records: int,
        client_ip: str,
        msisdn_factor: model.MSISDNFactor,
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        trace_id: UUID,
        source_endpoint: str,
        uploaded_by: str | None = None,
        account_id: int | None = None,
        imsi_list: list[IMSI] = [],
        msisdn_list: list[msisdn_type] = [],
        duplicate_imsi: list[IMSI] = [],
        duplicate_msisdn: list[MSISDN] = [],
        valid_data: list[dict] = [],
        invalid_data: list[dict] = [],
    ):
        msisdn_list = [msisdn_type(data.get("MSISDN")) for data in valid_data]
        self.validate_common_request(msisdn_list)
        sim_details = list(self.sim_repository.get_sim_cards(imsi_list=imsi_list))
        sim_imsi_lookup = {sim.imsi: sim.msisdn for sim in sim_details}
        sim_profile_lookup = {sim.imsi: sim.sim_profile for sim in sim_details}
        sim_cards = self.sim_repository.validate_bulk_sim_card_details(
            valid_data, imsi_list, msisdn_list  # type: ignore
        )
        msisdn_factor_lookup = {
            sim.requested_imsi: sim.msisdn_factor for sim in sim_cards
        }
        common_msisdn_factor = self.get_common_value(msisdn_factor_lookup)
        imsi_list_set = set(imsi_list)
        if (
            (common_msisdn_factor is not None)
            and (common_msisdn_factor != msisdn_factor)
        ) or (common_msisdn_factor is None):
            free_msisdn_list = self.sim_repository.get_msisdn_factor(
                msisdn_factor, len(imsi_list)
            )
            temp_imsi_list = imsi_list[:]
            for sim in sim_details:
                if (sim.allocation_id is not None) and (
                    SimStatus(sim.sim_status) == (SimStatus.ACTIVE or SimStatus.PENDING)
                ):
                    temp_imsi_list.remove(sim.imsi)
            valid_data = [
                {**record, MSISDN_FIELD: msisdn}
                for record, msisdn in zip(valid_data, free_msisdn_list)
                if not temp_imsi_list.remove(record["IMSI"])  # type: ignore
            ]
            msisdn_list = free_msisdn_list
            imsi_list = list(imsi_list_set - set(temp_imsi_list))
        if not imsi_list:
            raise exceptions.MSISDNNotFound("No free MSISDNs found.")
        (
            final_valid_results,
            final_valid_imsi_list,
            error_response,
        ) = self._validate_sim_card_details_by_imsi_msisdn(
            current_sim_details=sim_details,
            valid_imsi_list=imsi_list,
            duplicate_imsi=duplicate_imsi,
            valid_data=DataFrame(valid_data),
            valid_msisdn_list=msisdn_list,  # type: ignore
            duplicate_msisdn=duplicate_msisdn,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            invalid_records=invalid_data,
        )
        response = self.sim_repository.bulk_update_sim_card_details(
            all_sim_card_details=final_valid_results, sim_profile=sim_profile
        )

        if not response:
            logger.error(f"Response: {response}")
            raise exceptions.IMSIError("Unexpected error occoured.")

        final_valid_reponse = list(
            self.sim_repository.get_sim_cards(imsi_list=final_valid_imsi_list)
        )

        self.add_upload_file_status(
            trace_id=trace_id,
            field=MSISDN_FIELD,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            success_count=len(final_valid_imsi_list),
            failure_count=len(error_response),
            uploaded_by=uploaded_by,
            error_results=error_response,
            account_service=account_service,
            account_id=account_id,
        )

        logger.info(f"Total Records: {total_records}")
        logger.info(f"Error Details: {error_response}")

        audit_details = []
        if final_valid_imsi_list:
            for sim_detail in final_valid_reponse:
                prior_profile = sim_profile_lookup.get(sim_detail.imsi, None)
                prior_msisdn = sim_imsi_lookup.get(sim_detail.imsi, None)
                # Adding audit details
                if prior_msisdn != sim_detail.msisdn:
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_detail.imsi),
                            iccid=ICCID(sim_detail.iccid),
                            msisdn=MSISDN(sim_detail.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_msisdn,
                            new_value=MSISDN(sim_detail.msisdn),
                            field=MSISDN_FIELD,
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )
                if sim_detail.sim_profile and prior_profile != sim_detail.sim_profile:
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_detail.imsi),
                            iccid=ICCID(sim_detail.iccid),
                            msisdn=MSISDN(sim_detail.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_profile,
                            new_value=sim_detail.sim_profile,
                            field="SIM Profile",
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )

            streaming_service.submit_to_kafka(
                key=source_endpoint,
                topic=settings.UPLOAD_MSISDN_TOPIC,
                audit_details=audit_details,
            )
        return model.BulkSimCardUpdateResult(
            total_details=total_records,
            error_details=len(error_response),
            error_results=error_response,
        )

    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        return self.sim_repository.unallocate_sim_cards(imsi_list=imsi_list)

    def imsis_to_delete(self, imsi_list: list[IMSI]) -> model.IMSIDeleteResponse:
        try:
            dict_reponse_count = self.sim_repository.imsis_to_delete(
                imsis_list=imsi_list
            )
            deleted = dict_reponse_count.get("deleted_imsis_count", 0)

            message = f"{deleted} out of {len(imsi_list)} IMSIs were deleted."

            return model.IMSIDeleteResponse(message=message)

        except Exception as e:
            logger.error(f"Error in imsis_to_delete: {str(e)}")
            raise

    def _create_sim_action_audit(
        self,
        imsi,
        iccid,
        msisdn,
        field,
        action,
        client_ip,
        created_by,
        request_type,
        response,
        message=None,
    ) -> model.SIMActionAudit:
        return model.SIMActionAudit(
            uuid=uuid4(),
            imsi=imsi,
            iccid=iccid,
            msisdn=msisdn,
            request_type=request_type,
            field=field,
            action=action,
            client_ip=client_ip,
            created_by=created_by,
            response=response,
            message=message,
        )

    def create_flush_sim_response(
        self, pip_flush: model.SIMFlushResponse
    ) -> model.SIMFlushResponse:
        """Create SIM flush response object using the pip result"""
        sim_response = model.SIMFlushResponse(
            trid=pip_flush.trid,
            message=pip_flush.message,
            imsi=pip_flush.imsi,
            msisdn=pip_flush.msisdn,
        )
        return sim_response

    def flush_sim_state(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        client_ip: str | None = None,
    ) -> model.SIMFlushResponse:
        """Flush the SIM state and log the result"""
        pip_flush = self.provisioning.flush_sim(imsi, MSISDN(msisdn), client_ip)
        return pip_flush

    def flush_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMFlushResponse:
        try:
            sim_card = self.get_sim_card(imsi)
            iccid = ICCID(sim_card.iccid)
            msisdn = MSISDN(sim_card.msisdn)

            pip_flush = self.flush_sim_state(imsi, msisdn, client_ip)
            logger.info(f"Success external_api flush_sim response: {pip_flush}")

            sim_action_flush_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="SIM State",
                action="Send Cancel Location",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.FLUSH.name,
                response=pip_flush.message,
            )

            self.audit_service.add_sim_action_audit_api(sim_action_flush_log)
            logger.info(
                f"Audit entry for flush_sim_state success: {sim_action_flush_log}"
            )

            return self.create_flush_sim_response(pip_flush)

        except exceptions.SIMFlushError as e:
            logger.error(f"Error in flush_sim_state: {e}")
            sim_card = self.get_sim_card(imsi)
            iccid = ICCID(sim_card.iccid)
            msisdn = MSISDN(sim_card.msisdn)
            sim_action_flush_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="SIM State",
                action="Send Cancel Location",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.FLUSH.name,
                response=str(e),
            )
            self.audit_service.add_sim_action_audit_api(sim_action_flush_log)
            logger.error(f"Audit entry for sim flush failure: {sim_action_flush_log}")
            raise

    def create_pod_sim_response(
        self, pip_flush: model.SIMPODResponse
    ) -> model.SIMPODResponse:
        """Create SIM POD response object using the pip result"""
        sim_response = model.SIMPODResponse(
            trid=pip_flush.trid,
            message=pip_flush.message,
            imsi=pip_flush.imsi,
            msisdn=pip_flush.msisdn,
        )
        return sim_response

    def pod_sim_state(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        client_ip: str | None = None,
    ) -> model.SIMPODResponse:
        """POD (Disconnect) the SIM and log the result"""
        pip_pod = self.provisioning.pod_sim(imsi, MSISDN(msisdn), client_ip)
        return pip_pod

    def pod_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMPODResponse:
        try:
            sim_card = self.get_sim_card(imsi)
            iccid = ICCID(sim_card.iccid)
            msisdn = MSISDN(sim_card.msisdn)

            pip_pod = self.pod_sim_state(imsi, msisdn, client_ip)
            logger.info(f"Success external_api pod_sim response: {pip_pod}")

            pod_sim_state_audit_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="Send POD",
                action="Disconnect Data Session POD",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.POD.name,
                response=pip_pod.message,
            )

            self.audit_service.add_sim_action_audit_api(pod_sim_state_audit_log)
            logger.info(f"Success audit entry for pod_sim: {pod_sim_state_audit_log}")

            return self.create_pod_sim_response(pip_pod)

        except (exceptions.SIMPODError, exceptions.SIMActiveCallError) as e:
            logger.error(f"SIM POD Error: {e}")
            iccid = ICCID(sim_card.iccid)
            msisdn = MSISDN(sim_card.msisdn)
            pod_sim_state_audit_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="Send POD",
                action="Disconnect Data Session POD",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.POD.name,
                response=str(e),
            )

            logger.error(f"Error audit log: {pod_sim_state_audit_log}")
            self.audit_service.add_sim_action_audit_api(pod_sim_state_audit_log)
            raise

    def create_send_sms_sim_response(
        self, send_sms: model.SIMSendSMSResponse
    ) -> model.SIMSendSMSResponse:
        """Create SIM Send SMS response object using the pip result"""
        sim_response = model.SIMSendSMSResponse(
            trid=send_sms.trid,
            call_id=send_sms.call_id,
            imsi=send_sms.imsi,
            msisdn=send_sms.msisdn,
            message=send_sms.message,
        )
        return sim_response

    def sms_sim_state(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        message: str,
        client_ip: str | None = None,
    ) -> model.SIMSendSMSResponse:
        """Send SMS to SIM and log the result"""
        pip_send_sms = self.provisioning.sms_sim_ppl(
            imsi, MSISDN(msisdn), message, client_ip
        )
        return pip_send_sms

    def sms_sim(
        self,
        imsi: IMSI,
        message: str,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMSendSMSResponse:
        """Function for Send SMS to SIM"""
        try:
            sim_card = self.get_sim_card(IMSI(imsi))
            iccid = sim_card.iccid
            msisdn = sim_card.msisdn

            pip_send_sms = self.sms_sim_state(imsi, MSISDN(msisdn), message, client_ip)
            logger.info(f"Success pip_send_sms response: {pip_send_sms}")

            sms_sim_action_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="SMS",
                action="SMS",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.SMS.name,
                message=message,
                response=pip_send_sms.message,
            )
            self.audit_service.add_sim_action_audit_api(sms_sim_action_log)
            logger.info(f"Success audit log pip_send_sms: {sms_sim_action_log}")

            sim_response = self.create_send_sms_sim_response(pip_send_sms)
            return sim_response
        except exceptions.SIMSendSMSError as e:
            logger.error(f"SIM Send SMS Error: {e}")
            iccid = ICCID(sim_card.iccid)
            msisdn = MSISDN(sim_card.msisdn)
            sms_sim_action_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="SMS",
                action="SMS",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.SMS.name,
                message=message,
                response=str(e),
            )
            self.audit_service.add_sim_action_audit_api(sms_sim_action_log)
            logger.error(f"SMS action audit log: {sms_sim_action_log}")
            raise

    def apn_sim_using_provisioning(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        apn_id: str,
        sim_apn_action: model.SIMAPNAction,
        client_ip: str | None = None,
    ):
        """Update the SIM APN using the provisioning object and log the result"""
        ppl_apn = self.provisioning.apn_sim(
            imsi,
            MSISDN(msisdn),
            apn_id,
            sim_apn_action,
            client_ip,
        )
        if ppl_apn.status in {"Invalid", "Error"}:
            raise exceptions.SimAPNError("SIM APN update fail.")
        return ppl_apn

    def create_sim_apn_response(
        self, ppl_apn: model.SIMAPNResponse
    ) -> model.SIMAPNResponse:
        """Create SIM update APN response object using the provisioning result"""
        sim_response = model.SIMAPNResponse(
            message=ppl_apn.message,
            status=ppl_apn.status,
        )
        return sim_response

    def apn_sim(
        self,
        imsi: IMSI,
        apn_id: str,
        created_by: str,
        sim_apn_action: model.SIMAPNAction,
        client_ip: str | None = None,
    ) -> model.SIMAPNResponse:
        """Function for updateing APN SIM"""
        try:
            sim_card = self.get_sim_card(IMSI(imsi))
            iccid = sim_card.iccid
            msisdn = sim_card.msisdn

            ppl_apn = self.apn_sim_using_provisioning(
                imsi,
                MSISDN(msisdn),
                apn_id,
                sim_apn_action,
                client_ip,
            )
            logger.info(f"Success apn_sim external_api response: {ppl_apn}")

            apn_sim_action_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="APN",
                action="Updated",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.APN.name,
                response=ppl_apn.message,
            )
            self.audit_service.add_sim_action_audit_api(apn_sim_action_log)

            logger.info(f"Success APN sim action log: {apn_sim_action_log}")

            sim_response = self.create_sim_apn_response(ppl_apn)
            return sim_response
        except exceptions.SimAPNError as ex:
            iccid = ICCID(sim_card.iccid)
            msisdn = MSISDN(sim_card.msisdn)
            apn_sim_action_log = self._create_sim_action_audit(
                imsi=IMSI(imsi),
                iccid=ICCID(iccid),
                msisdn=MSISDN(msisdn),
                field="APN",
                action="Updated",
                client_ip=client_ip,
                created_by=created_by,
                request_type=model.RequestType.APN.name,
                response=str(ex),
            )
            self.audit_service.add_sim_action_audit_api(apn_sim_action_log)

            logger.error(
                f"Error APN sim action log: {apn_sim_action_log} having error: {ex}"
            )

            raise

    def get_latest_data_session(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
    ) -> model.DataSession:
        """Get latest data session for a SIM by IMSI"""
        try:
            # First, get the SIM info to fetch ICCID from IMSI
            sim_info = account_service.get_sim_account_info(search=imsi)
            logger.info(f"Getting latest data session for ICCID: {sim_info.iccid}")

            # Get the data session using the ICCID
            data_session = self.provisioning.get_latest_data_session(sim_info.iccid)
            logger.info(f"Success data session response: {data_session}")
            return data_session
        except SimAccountDataNotFound:
            logger.error(f"Sim account data not found for IMSI: {imsi}")
            raise
        except Exception as e:
            logger.error(f"Error getting data session for IMSI {imsi}: {e}")
            raise

    def get_location(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
        page_number: int = 1,
        page_size: int = 100,
    ) -> model.LocationResponse:
        """Get location data for a SIM by IMSI"""
        try:
            # First, get the SIM info to fetch ICCID from IMSI
            sim_info = account_service.get_sim_account_info(search=imsi)
            logger.info(f"Getting location data for ICCID: {sim_info.iccid}")

            # Get the location data using the ICCID
            location_response = self.provisioning.get_location(
                iccid=sim_info.iccid,
                page_number=page_number,
                page_size=page_size,
            )
            logger.info(f"Success location response: {location_response}")
            return location_response
        except SimAccountDataNotFound:
            logger.error(f"Sim account data not found for IMSI: {imsi}")
            raise
        except Exception as e:
            logger.error(f"Error getting location for IMSI {imsi}: {e}")
            raise

    def get_cell_location(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
    ) -> model.CellLocation:
        """Get cell location data from external API"""
        try:
            sim_info = account_service.get_sim_account_info(search=imsi)
            logger.info(f"Getting cell location for IMSI: {imsi}")
            mcc_mnc_details = self.fetch_mcc_mnc_ex(sim_info.msisdn, None)
            logger.info(f"mcc_mnc_details: {mcc_mnc_details}")

            # Get the cell location data using the external API
            cell_location = self.provisioning.get_cell_location(
                mcc=mcc_mnc_details.mcc,
                mnc=mcc_mnc_details.mnc,
                lac=mcc_mnc_details.lac,
                cell=mcc_mnc_details.cell_id,
            )
            logger.info(f"Success cell location response: {cell_location}")
            return cell_location
        except Exception as e:
            logger.error(f"Error getting cell location: {e}")
            raise

    def worldov_token(self) -> model.TokenResponse:
        token_response = self.provisioning.worldov_token()
        return token_response

    def worldov_token_refresh(
        self, refresh_token: model.WorldOVTokenRefresh
    ) -> model.TokenResponse:
        token_response = self.provisioning.worldov_token_refresh(refresh_token)
        return token_response

    def get_sim_card_by_iccid(self, iccid: ICCID) -> model.IMSIDetails:
        try:
            sim_cards = self.sim_repository.get_imsis(iccids=[iccid])
            sim_card = next(sim_cards)
        except StopIteration:
            logger.error(f"SIM card with ICCID '{iccid}' not found.")
            raise exceptions.SimCardsNotFound(
                f"SIM card with ICCID '{iccid}' not found."
            )

        if not sim_card.imsi:
            logger.error(f"IMSI not found for ICCID '{iccid}'.")
            raise exceptions.IMSIDoesNotExit(f"IMSI not found for ICCID '{iccid}'.")

        return sim_card

    def fetch_mcc_mnc_ex(
        self,
        msisdn: msisdn_type,
        client_ip: str | None = None,
    ) -> model.SIMFetchMNCMCC:

        pip_fetch_mcc_mnc = self.provisioning.fetch_mcc_mnc_ex_ppl(
            msisdn=msisdn,
            client_ip=client_ip,
        )
        return pip_fetch_mcc_mnc

    def fetch_mcc_mnc(
        self,
        imsi: IMSI,
        account_service: AbstractAccountService,
        client_ip: str | None = None,
    ) -> model.SIMFetchMNCMCC:
        try:
            sim_card_ = account_service.get_sim_account_info(search=imsi)
            msisdn = sim_card_.msisdn

            logger.info(f"sim card details: {sim_card_}")
            if sim_card_:
                logger.info(f"msisdn: {msisdn}, client_ip: {client_ip}")
                return self.fetch_mcc_mnc_ex(msisdn, client_ip)
            else:
                raise exceptions.IMSIDoesNotExit(
                    f"IMSI does not exist for given iccid: {msisdn}"
                )

        except (exceptions.SimCardsNotFound, exceptions.IMSIDoesNotExit) as known_exc:
            logger.error(f"Known error for ICCID '{msisdn}': {known_exc}")
            raise
